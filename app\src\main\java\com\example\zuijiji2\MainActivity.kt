package com.example.zuijiji2

import android.net.Uri
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.gestures.detectDragGestures
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.clipToBounds
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.center
import androidx.compose.ui.graphics.*
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.drawscope.clipPath
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.sp
import androidx.compose.ui.viewinterop.AndroidView
import coil.compose.rememberAsyncImagePainter
import coil.request.ImageRequest
import com.example.zuijiji2.opengl.LiquidGlassView
import com.example.zuijiji2.ui.theme.Zuijiji2Theme

class MainActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContent {
            Zuijiji2Theme {
                MainScreen()
            }
        }
    }
}

@Composable
fun MainScreen() {
    var backgroundImageUri by remember { mutableStateOf<Uri?>(null) }
    var showFloatingWindow by remember { mutableStateOf(false) }
    var showLiquidGlass by remember { mutableStateOf(false) }
    var showCustomLiquidGlass by remember { mutableStateOf(false) }
    var windowOffset by remember { mutableStateOf(Offset(100f, 100f)) }
    val context = LocalContext.current
    val density = LocalDensity.current

    // 液体玻璃视图引用
    var liquidGlassView by remember { mutableStateOf<LiquidGlassView?>(null) }
    var customLiquidGlassView by remember { mutableStateOf<LiquidGlassView?>(null) }
    var controlPanelGlassView by remember { mutableStateOf<LiquidGlassView?>(null) }

    // 液体玻璃的尺寸控制（两个模式共用）
    var glassWidth by remember { mutableStateOf(1000f) }
    var glassHeight by remember { mutableStateOf(800f) }

    // 图片选择器
    val imagePickerLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.GetContent()
    ) { uri: Uri? ->
        backgroundImageUri = uri
        // 同时更新液体玻璃视图的背景
        uri?.let {
            liquidGlassView?.let { view ->
                try {
                    val inputStream = context.contentResolver.openInputStream(uri)
                    inputStream?.use { stream ->
                        val bitmap = android.graphics.BitmapFactory.decodeStream(stream)
                        view.setBackgroundBitmap(bitmap)
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
            // 同时更新自定义液体玻璃视图的背景
            customLiquidGlassView?.let { view ->
                try {
                    val inputStream = context.contentResolver.openInputStream(uri)
                    inputStream?.use { stream ->
                        val bitmap = android.graphics.BitmapFactory.decodeStream(stream)
                        view.setBackgroundBitmap(bitmap)
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
            // 同时更新控制面板液体玻璃视图的背景
            controlPanelGlassView?.let { view ->
                try {
                    val inputStream = context.contentResolver.openInputStream(uri)
                    inputStream?.use { stream ->
                        val bitmap = android.graphics.BitmapFactory.decodeStream(stream)
                        view.setBackgroundBitmap(bitmap)
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
        }
    }

    BoxWithConstraints(
        modifier = Modifier.fillMaxSize()
    ) {
        val maxWidth = maxWidth
        val maxHeight = maxHeight
        // 背景图片
        backgroundImageUri?.let { uri ->
            Image(
                painter = rememberAsyncImagePainter(
                    ImageRequest.Builder(context)
                        .data(uri)
                        .build()
                ),
                contentDescription = "背景图片",
                modifier = Modifier.fillMaxSize(),
                contentScale = ContentScale.Crop
            )
        } ?: run {
            // 默认背景色
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .background(Color.LightGray)
            )
        }

        // 主要内容

        // 液体玻璃效果视图
        if (showLiquidGlass) {
            LiquidGlassWithText(
                liquidGlassView = liquidGlassView,
                onViewCreated = { view -> liquidGlassView = view },
                glassWidth = glassWidth,
                glassHeight = glassHeight,
                backgroundImageUri = backgroundImageUri,
                context = context
            )

            // 确保在显示液体玻璃时设置背景
            LaunchedEffect(showLiquidGlass, backgroundImageUri) {
                if (showLiquidGlass && backgroundImageUri != null) {
                    liquidGlassView?.let { view ->
                        try {
                            val inputStream = context.contentResolver.openInputStream(backgroundImageUri!!)
                            inputStream?.use { stream ->
                                val bitmap = android.graphics.BitmapFactory.decodeStream(stream)
                                view.setBackgroundBitmap(bitmap)
                            }
                        } catch (e: Exception) {
                            e.printStackTrace()
                        }
                    }
                }
            }
        }

        // 自定义液体玻璃效果视图
        if (showCustomLiquidGlass) {
            Box(modifier = Modifier.fillMaxSize()) {
                // 液体玻璃与文本组合
                LiquidGlassWithText(
                    liquidGlassView = customLiquidGlassView,
                    onViewCreated = { view -> customLiquidGlassView = view },
                    glassWidth = glassWidth,
                    glassHeight = glassHeight,
                    backgroundImageUri = backgroundImageUri,
                    context = context
                )

                // 控制面板 - 使用液体玻璃背景效果
                Box(
                    modifier = Modifier
                        .align(Alignment.TopEnd)
                        .padding(16.dp)
                ) {
                    // 液体玻璃背景层
                    AndroidView(
                        factory = { context ->
                            LiquidGlassView(context).also { view ->
                                controlPanelGlassView = view
                                // 设置一个较小的液体玻璃作为背景
                                view.setCustomGlassSize(280f, 200f)
                                backgroundImageUri?.let { uri ->
                                    try {
                                        val inputStream = context.contentResolver.openInputStream(uri)
                                        inputStream?.use { stream ->
                                            val bitmap = android.graphics.BitmapFactory.decodeStream(stream)
                                            view.setBackgroundBitmap(bitmap)
                                        }
                                    } catch (e: Exception) {
                                        e.printStackTrace()
                                    }
                                }
                            }
                        },
                        update = { view ->
                            // 动态更新背景图片
                            backgroundImageUri?.let { uri ->
                                try {
                                    val inputStream = context.contentResolver.openInputStream(uri)
                                    inputStream?.use { stream ->
                                        val bitmap = android.graphics.BitmapFactory.decodeStream(stream)
                                        view.setBackgroundBitmap(bitmap)
                                    }
                                } catch (e: Exception) {
                                    e.printStackTrace()
                                }
                            }
                        },
                        modifier = Modifier
                            .size(280.dp, 200.dp)
                            .clip(androidx.compose.foundation.shape.RoundedCornerShape(12.dp))
                    )

                    // 半透明遮罩层，确保文字可读性
                    Box(
                        modifier = Modifier
                            .size(280.dp, 200.dp)
                            .background(
                                Color.Black.copy(alpha = 0.3f),
                                shape = androidx.compose.foundation.shape.RoundedCornerShape(12.dp)
                            )
                    )

                    // 控制内容
                    Column(
                        modifier = Modifier
                            .size(280.dp, 200.dp)
                            .padding(16.dp)
                    ) {
                    Text(
                        text = "液体玻璃尺寸控制",
                        color = Color.White,
                        style = MaterialTheme.typography.titleSmall
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    // 宽度控制
                    Text(
                        text = "宽度: ${glassWidth.toInt()}px",
                        color = Color.White,
                        style = MaterialTheme.typography.bodySmall
                    )
                    Slider(
                        value = glassWidth,
                        onValueChange = { glassWidth = it },
                        valueRange = 100f..1600f,
                        modifier = Modifier.width(200.dp)
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    // 高度控制
                    Text(
                        text = "高度: ${glassHeight.toInt()}px",
                        color = Color.White,
                        style = MaterialTheme.typography.bodySmall
                    )
                    Slider(
                        value = glassHeight,
                        onValueChange = { glassHeight = it },
                        valueRange = 100f..1600f,
                        modifier = Modifier.width(200.dp)
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                        // 关闭按钮
                        Button(
                            onClick = { showCustomLiquidGlass = false },
                            colors = ButtonDefaults.buttonColors(
                                containerColor = MaterialTheme.colorScheme.error
                            )
                        ) {
                            Text("关闭", color = Color.White)
                        }
                    }
                }
            }

            // 确保在显示自定义液体玻璃时设置背景
            LaunchedEffect(showCustomLiquidGlass, backgroundImageUri) {
                if (showCustomLiquidGlass && backgroundImageUri != null) {
                    customLiquidGlassView?.let { view ->
                        try {
                            val inputStream = context.contentResolver.openInputStream(backgroundImageUri!!)
                            inputStream?.use { stream ->
                                val bitmap = android.graphics.BitmapFactory.decodeStream(stream)
                                view.setBackgroundBitmap(bitmap)
                            }
                        } catch (e: Exception) {
                            e.printStackTrace()
                        }
                    }
                }
            }
        }

        // 底部按钮行
        Column(
            modifier = Modifier
                .align(Alignment.BottomCenter)
                .offset(y = -20.dp)
                .padding(16.dp)
                .fillMaxWidth()
        ) {
            // 第一行：模式切换按钮
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                Button(
                    onClick = {
                        if (backgroundImageUri != null) {
                            showLiquidGlass = !showLiquidGlass
                            if (showLiquidGlass) {
                                showFloatingWindow = false
                                showCustomLiquidGlass = false
                            }
                        }
                    },
                    enabled = backgroundImageUri != null,
                    colors = ButtonDefaults.buttonColors(
                        containerColor = if (showLiquidGlass)
                            MaterialTheme.colorScheme.tertiary
                        else if (backgroundImageUri != null)
                            MaterialTheme.colorScheme.outline
                        else
                            MaterialTheme.colorScheme.surfaceVariant
                    ),
                    modifier = Modifier.weight(1f)
                ) {
                    Text(
                        text = when {
                            backgroundImageUri == null -> "请先导入图片"
                            showLiquidGlass -> "液体玻璃模式"
                            else -> "切换到液体玻璃"
                        },
                        style = MaterialTheme.typography.bodySmall
                    )
                }

                Spacer(modifier = Modifier.width(8.dp))

                Button(
                    onClick = {
                        if (backgroundImageUri != null) {
                            showCustomLiquidGlass = !showCustomLiquidGlass
                            if (showCustomLiquidGlass) {
                                showFloatingWindow = false
                                showLiquidGlass = false
                            }
                        }
                    },
                    enabled = backgroundImageUri != null,
                    colors = ButtonDefaults.buttonColors(
                        containerColor = if (showCustomLiquidGlass)
                            MaterialTheme.colorScheme.tertiary
                        else if (backgroundImageUri != null)
                            MaterialTheme.colorScheme.outline
                        else
                            MaterialTheme.colorScheme.surfaceVariant
                    ),
                    modifier = Modifier.weight(1f)
                ) {
                    Text(
                        text = when {
                            backgroundImageUri == null -> "请先导入图片"
                            showCustomLiquidGlass -> "自定义液体玻璃"
                            else -> "自定义液体玻璃"
                        },
                        style = MaterialTheme.typography.bodySmall
                    )
                }
            }

            Spacer(modifier = Modifier.height(12.dp))

            // 第二行：功能按钮
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                // 左边：导入图片按钮
                Button(
                    onClick = {
                        imagePickerLauncher.launch("image/*")
                    },
                    modifier = Modifier.weight(1f),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = MaterialTheme.colorScheme.primary
                    )
                ) {
                    Text(
                        text = "导入图片",
                        style = MaterialTheme.typography.bodyMedium
                    )
                }

                Spacer(modifier = Modifier.width(16.dp))

                // 右边：打开弹窗按钮（仅在非液体玻璃模式下显示）
                Button(
                    onClick = {
                        if (!showLiquidGlass && !showCustomLiquidGlass) {
                            showFloatingWindow = true
                        }
                    },
                    modifier = Modifier.weight(1f),
                    enabled = !showLiquidGlass && !showCustomLiquidGlass,
                    colors = ButtonDefaults.buttonColors(
                        containerColor = MaterialTheme.colorScheme.secondary
                    )
                ) {
                    Text(
                        text = "打开放大镜",
                        style = MaterialTheme.typography.bodyMedium
                    )
                }
            }
        }

        // 可拖动的圆形放大镜弹窗
        if (showFloatingWindow) {
            Box(
                modifier = Modifier
                    .offset {
                        IntOffset(
                            x = windowOffset.x.toInt(),
                            y = windowOffset.y.toInt()
                        )
                    }
                    .size(100.dp)
                    .pointerInput(Unit) {
                        detectDragGestures(
                            onDragStart = { },
                            onDragEnd = { }
                        ) { change, dragAmount ->
                            change.consume()
                            val windowSizePx = with(density) { 100.dp.toPx() }
                            val maxWidthPx = with(density) { maxWidth.toPx() }
                            val maxHeightPx = with(density) { maxHeight.toPx() }

                            windowOffset = Offset(
                                x = (windowOffset.x + dragAmount.x).coerceIn(
                                    0f,
                                    maxWidthPx - windowSizePx
                                ),
                                y = (windowOffset.y + dragAmount.y).coerceIn(
                                    0f,
                                    maxHeightPx - windowSizePx
                                )
                            )
                        }
                    }
            ) {
                // 优化后的复合放大镜 - 单个Canvas绘制所有层级
                OptimizedMagnifyingGlass(
                    modifier = Modifier.size(100.dp),
                    backgroundImageUri = backgroundImageUri,
                    magnificationCenter = Offset(
                        windowOffset.x + with(density) { 50.dp.toPx() },
                        windowOffset.y + with(density) { 50.dp.toPx() }
                    ),
                    screenSize = Offset(
                        with(density) { maxWidth.toPx() },
                        with(density) { maxHeight.toPx() }
                    )
                )
            }
        }
    }
}

@Composable
fun OptimizedMagnifyingGlass(
    modifier: Modifier = Modifier,
    backgroundImageUri: Uri?,
    magnificationCenter: Offset,
    screenSize: Offset
) {
    val context = LocalContext.current
    val density = LocalDensity.current

    // 获取背景图片的ImageBitmap
    var imageBitmap by remember { mutableStateOf<ImageBitmap?>(null) }

    LaunchedEffect(backgroundImageUri) {
        backgroundImageUri?.let { uri ->
            try {
                val inputStream = context.contentResolver.openInputStream(uri)
                inputStream?.use { stream ->
                    val bitmap = android.graphics.BitmapFactory.decodeStream(stream)
                    imageBitmap = bitmap.asImageBitmap()
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    Canvas(
        modifier = modifier.clipToBounds()
    ) {
        val canvasSize = size
        val canvasCenter = canvasSize.center

        // 预计算图片显示参数
        val imageParams = imageBitmap?.let { bitmap ->
            calculateImageDisplayParams(bitmap, screenSize)
        }

        // 绘制所有层级的放大镜
        imageParams?.let { params ->
            // 1. 绘制大圆形放大镜（底层，30倍放大，有边框）
            drawMagnifiedLayer(
                imageBitmap = imageBitmap!!,
                imageParams = params,
                magnificationCenter = magnificationCenter,
                layerRadius = with(density) { 50.dp.toPx() },
                magnificationFactor = 30f,
                canvasCenter = canvasCenter,
                showBorder = true
            )

            // 2. 绘制60个中间层圆形放大镜
            for (i in 1..60) {
                val layerSize = 100.0 - i * 0.1
                val layerRadius = with(density) { (layerSize / 2).dp.toPx() }
                val magnification = 30f - i * 0.2f

                drawMagnifiedLayer(
                    imageBitmap = imageBitmap!!,
                    imageParams = params,
                    magnificationCenter = magnificationCenter,
                    layerRadius = layerRadius,
                    magnificationFactor = magnification,
                    canvasCenter = canvasCenter,
                    showBorder = false
                )
            }

            // 3. 绘制小圆形放大镜（最上层，0.82倍放大，无边框）
            drawMagnifiedLayer(
                imageBitmap = imageBitmap!!,
                imageParams = params,
                magnificationCenter = magnificationCenter,
                layerRadius = with(density) { 35.dp.toPx() },
                magnificationFactor = 2f,
                canvasCenter = canvasCenter,
                showBorder = false
            )
        } ?: run {
            // 如果没有背景图片，显示默认的灰色背景
            drawCircle(
                color = Color.LightGray,
                radius = with(density) { 50.dp.toPx() },
                center = canvasCenter
            )
        }
    }
}

// 数据类用于存储图片显示参数
data class ImageDisplayParams(
    val displayWidth: Float,
    val displayHeight: Float,
    val offsetX: Float,
    val offsetY: Float,
    val bitmapWidth: Int,
    val bitmapHeight: Int
)

// 计算图片显示参数的辅助函数
fun calculateImageDisplayParams(bitmap: ImageBitmap, screenSize: Offset): ImageDisplayParams {
    val imageAspectRatio = bitmap.width.toFloat() / bitmap.height.toFloat()
    val screenAspectRatio = screenSize.x / screenSize.y

    val (imageDisplayWidth, imageDisplayHeight, imageOffsetX, imageOffsetY) = if (imageAspectRatio > screenAspectRatio) {
        // 图片比屏幕宽，高度填满，宽度裁剪
        val displayHeight = screenSize.y
        val displayWidth = displayHeight * imageAspectRatio
        val offsetX = (displayWidth - screenSize.x) / 2f
        arrayOf(displayWidth, displayHeight, -offsetX, 0f)
    } else {
        // 图片比屏幕高，宽度填满，高度裁剪
        val displayWidth = screenSize.x
        val displayHeight = displayWidth / imageAspectRatio
        val offsetY = (displayHeight - screenSize.y) / 2f
        arrayOf(displayWidth, displayHeight, 0f, -offsetY)
    }

    return ImageDisplayParams(
        displayWidth = imageDisplayWidth,
        displayHeight = imageDisplayHeight,
        offsetX = imageOffsetX,
        offsetY = imageOffsetY,
        bitmapWidth = bitmap.width,
        bitmapHeight = bitmap.height
    )
}

// 绘制单个放大镜层级的辅助函数
fun DrawScope.drawMagnifiedLayer(
    imageBitmap: ImageBitmap,
    imageParams: ImageDisplayParams,
    magnificationCenter: Offset,
    layerRadius: Float,
    magnificationFactor: Float,
    canvasCenter: Offset,
    showBorder: Boolean
) {
    // 创建圆形裁剪路径
    val clipPath = Path().apply {
        addOval(
            androidx.compose.ui.geometry.Rect(
                center = canvasCenter,
                radius = layerRadius
            )
        )
    }

    clipPath(clipPath) {
        // 计算放大镜中心在图片坐标系中的位置
        val imageX = ((magnificationCenter.x - imageParams.offsetX) / imageParams.displayWidth) * imageParams.bitmapWidth
        val imageY = ((magnificationCenter.y - imageParams.offsetY) / imageParams.displayHeight) * imageParams.bitmapHeight

        // 计算要截取的源区域大小
        val sourceRadius = layerRadius / magnificationFactor
        val sourceSize = sourceRadius * 2
        val sourceSizeInImage = sourceSize * (imageParams.bitmapWidth / imageParams.displayWidth)

        // 计算源区域的边界
        val srcLeft = (imageX - sourceSizeInImage / 2).coerceAtLeast(0f)
        val srcTop = (imageY - sourceSizeInImage / 2).coerceAtLeast(0f)
        val srcRight = (imageX + sourceSizeInImage / 2).coerceAtMost(imageParams.bitmapWidth.toFloat())
        val srcBottom = (imageY + sourceSizeInImage / 2).coerceAtMost(imageParams.bitmapHeight.toFloat())

        // 计算目标绘制区域
        val dstLeft = canvasCenter.x - layerRadius
        val dstTop = canvasCenter.y - layerRadius
        val dstSize = layerRadius * 2

        // 绘制放大的图片
        drawImage(
            image = imageBitmap,
            srcOffset = androidx.compose.ui.unit.IntOffset(
                srcLeft.toInt(),
                srcTop.toInt()
            ),
            srcSize = androidx.compose.ui.unit.IntSize(
                (srcRight - srcLeft).toInt(),
                (srcBottom - srcTop).toInt()
            ),
            dstOffset = androidx.compose.ui.unit.IntOffset(
                dstLeft.toInt(),
                dstTop.toInt()
            ),
            dstSize = androidx.compose.ui.unit.IntSize(
                dstSize.toInt(),
                dstSize.toInt()
            )
        )
    }

    // 绘制边框（如果需要）
    if (showBorder) {
        drawCircle(
            color = Color.White,
            radius = layerRadius,
            center = canvasCenter,
            style = Stroke(width = 1.dp.toPx())
        )
    }
}

@Composable
fun MagnifyingGlass(
    modifier: Modifier = Modifier,
    backgroundImageUri: Uri?,
    magnificationCenter: Offset,
    screenSize: Offset,
    magnificationFactor: Float = 8.0f,
    showBorder: Boolean = true
) {
    val context = LocalContext.current

    // 获取背景图片的ImageBitmap
    var imageBitmap by remember { mutableStateOf<ImageBitmap?>(null) }

    LaunchedEffect(backgroundImageUri) {
        backgroundImageUri?.let { uri ->
            try {
                val inputStream = context.contentResolver.openInputStream(uri)
                inputStream?.use { stream ->
                    val bitmap = android.graphics.BitmapFactory.decodeStream(stream)
                    imageBitmap = bitmap.asImageBitmap()
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    Canvas(
        modifier = modifier
            .clip(CircleShape)
            .let { mod ->
                if (showBorder) {
                    mod.border(
                        width = 0.dp,
                        color = Color.White,
                        shape = CircleShape
                    )
                } else {
                    mod
                }
            }
    ) {
        val radius = size.minDimension / 2f
        val center = size.center

        // 创建圆形裁剪路径
        val clipPath = Path().apply {
            addOval(
                androidx.compose.ui.geometry.Rect(
                    center = center,
                    radius = radius
                )
            )
        }

        clipPath(clipPath) {
            imageBitmap?.let { bitmap ->
                // 计算图片的缩放比例
                val imageAspectRatio = bitmap.width.toFloat() / bitmap.height.toFloat()
                val screenAspectRatio = screenSize.x / screenSize.y

                // 计算图片在屏幕上的实际显示区域（考虑ContentScale.Crop）
                val (imageDisplayWidth, imageDisplayHeight, imageOffsetX, imageOffsetY) = if (imageAspectRatio > screenAspectRatio) {
                    // 图片比屏幕宽，高度填满，宽度裁剪
                    val displayHeight = screenSize.y
                    val displayWidth = displayHeight * imageAspectRatio
                    val offsetX = (displayWidth - screenSize.x) / 2f
                    arrayOf(displayWidth, displayHeight, -offsetX, 0f)
                } else {
                    // 图片比屏幕高，宽度填满，高度裁剪
                    val displayWidth = screenSize.x
                    val displayHeight = displayWidth / imageAspectRatio
                    val offsetY = (displayHeight - screenSize.y) / 2f
                    arrayOf(displayWidth, displayHeight, 0f, -offsetY)
                }

                // 计算放大镜中心在图片坐标系中的位置
                val imageX = ((magnificationCenter.x - imageOffsetX) / imageDisplayWidth) * bitmap.width
                val imageY = ((magnificationCenter.y - imageOffsetY) / imageDisplayHeight) * bitmap.height

                // 计算要截取的源区域大小
                val sourceRadius = radius / magnificationFactor
                val sourceSize = sourceRadius * 2
                val sourceSizeInImage = sourceSize * (bitmap.width / imageDisplayWidth)

                // 计算源区域的边界
                val srcLeft = (imageX - sourceSizeInImage / 2).coerceAtLeast(0f)
                val srcTop = (imageY - sourceSizeInImage / 2).coerceAtLeast(0f)
                val srcRight = (imageX + sourceSizeInImage / 2).coerceAtMost(bitmap.width.toFloat())
                val srcBottom = (imageY + sourceSizeInImage / 2).coerceAtMost(bitmap.height.toFloat())

                // 绘制放大的图片
                drawImage(
                    image = bitmap,
                    srcOffset = androidx.compose.ui.unit.IntOffset(
                        srcLeft.toInt(),
                        srcTop.toInt()
                    ),
                    srcSize = androidx.compose.ui.unit.IntSize(
                        (srcRight - srcLeft).toInt(),
                        (srcBottom - srcTop).toInt()
                    ),
                    dstOffset = androidx.compose.ui.unit.IntOffset(0, 0),
                    dstSize = androidx.compose.ui.unit.IntSize(
                        size.width.toInt(),
                        size.height.toInt()
                    )
                )
            } ?: run {
                // 如果没有背景图片，显示默认的灰色背景
                drawCircle(
                    color = Color.LightGray,
                    radius = radius,
                    center = center
                )
            }
        }
    }
}

@Composable
fun LiquidGlassWithText(
    liquidGlassView: LiquidGlassView?,
    onViewCreated: (LiquidGlassView) -> Unit,
    glassWidth: Float,
    glassHeight: Float,
    backgroundImageUri: Uri?,
    context: android.content.Context
) {
    var glassPosition by remember { mutableStateOf(Offset(0f, 0f)) }

    Box(modifier = Modifier.fillMaxSize()) {
        AndroidView(
            factory = { context ->
                LiquidGlassView(context).also { view ->
                    onViewCreated(view)
                }
            },
            update = { view ->
                // 更新自定义尺寸
                view.setCustomGlassSize(glassWidth, glassHeight)
                // 每次更新时检查是否需要设置背景图片
                backgroundImageUri?.let { uri ->
                    try {
                        val inputStream = context.contentResolver.openInputStream(uri)
                        inputStream?.use { stream ->
                            val bitmap = android.graphics.BitmapFactory.decodeStream(stream)
                            view.setBackgroundBitmap(bitmap)
                        }
                    } catch (e: Exception) {
                        e.printStackTrace()
                    }
                }

                // 获取当前玻璃位置
                val currentPos = view.getCurrentGlassCenter()
                glassPosition = Offset(currentPos.first, currentPos.second)
            },
            modifier = Modifier.fillMaxSize()
        )

        // 跟随液体玻璃移动的"嘿嘿嘿"文本
        liquidGlassView?.let {
            Text(
                text = "嘿嘿嘿",
                style = MaterialTheme.typography.headlineLarge.copy(
                    fontSize = 48.sp,
                    fontWeight = androidx.compose.ui.text.font.FontWeight.Bold
                ),
                color = Color.White,
                modifier = Modifier
                    .offset {
                        IntOffset(
                            (glassPosition.x - 80.dp.toPx()).toInt(), // 80dp是文本宽度的一半
                            (glassPosition.y - 24.dp.toPx()).toInt()  // 24dp是文本高度的一半
                        )
                    }
            )
        }
    }
}

@Composable
fun Greeting(name: String, modifier: Modifier = Modifier) {
    Text(
        text = "Hello $name!",
        modifier = modifier
    )
}

@Preview(showBackground = true)
@Composable
fun MainScreenPreview() {
    Zuijiji2Theme {
        MainScreen()
    }
}

@Preview(showBackground = true)
@Composable
fun GreetingPreview() {
    Zuijiji2Theme {
        Greeting("Android")
    }
}