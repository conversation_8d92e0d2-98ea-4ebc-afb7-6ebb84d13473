package com.example.zuijiji2.opengl

object SimpleTestShaders {
    
    // 简单的顶点着色器
    const val VERTEX_SHADER = """
        #version 300 es
        precision mediump float;
        
        layout(location = 0) in vec2 position;
        layout(location = 1) in vec2 texCoord;
        
        out vec2 fragTexCoord;
        
        void main() {
            gl_Position = vec4(position, 0.0, 1.0);
            // 修复纹理坐标，避免图片旋转
            fragTexCoord = vec2(texCoord.x, 1.0 - texCoord.y);
        }
    """
    
    // 简单的片段着色器 - 只显示背景纹理和一个简单的圆形效果
    const val FRAGMENT_SHADER = """
        #version 300 es
        precision mediump float;
        
        in vec2 fragTexCoord;
        out vec4 outColor;
        
        uniform sampler2D backgroundTexture;
        uniform vec2 glassCenter;
        uniform float glassRadius;
        uniform vec2 resolution;
        
        void main() {
            // 获取背景颜色
            vec3 backgroundColor = texture(backgroundTexture, fragTexCoord).rgb;
            
            // 计算当前像素到玻璃中心的距离
            vec2 pixelCoord = vec2(fragTexCoord.x * resolution.x, fragTexCoord.y * resolution.y);
            vec2 d = pixelCoord - glassCenter;
            float distance = length(d);
            
            vec3 finalColor = backgroundColor;
            
            // 如果在玻璃范围内，应用简单的放大效果
            if (distance <= glassRadius) {
                // 简单的放大效果
                float magnification = 1.5;
                vec2 offset = d * (1.0 - magnification) * 0.5;
                vec2 sampleCoord = (pixelCoord + offset) / resolution;
                sampleCoord = clamp(sampleCoord, 0.0, 1.0);
                
                finalColor = texture(backgroundTexture, sampleCoord).rgb;
                
                // 添加一个简单的边缘高光
                float edgeFactor = 1.0 - (distance / glassRadius);
                finalColor += vec3(0.2) * edgeFactor * edgeFactor;
            }
            
            outColor = vec4(finalColor, 1.0);
        }
    """
}
