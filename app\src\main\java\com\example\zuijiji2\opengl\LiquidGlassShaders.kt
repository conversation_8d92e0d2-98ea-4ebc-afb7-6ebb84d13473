package com.example.zuijiji2.opengl

object LiquidGlassShaders {
    
    // Vertex Shader - OpenGL ES 3.0 版本
    const val VERTEX_SHADER = """
        #version 300 es
        precision highp float;
        
        layout(location = 0) in vec2 position;
        layout(location = 1) in vec2 texCoord;
        
        out vec2 fragTexCoord;
        
        void main() {
            gl_Position = vec4(position, 0.0, 1.0);
            // 修复纹理坐标，避免图片旋转
            fragTexCoord = vec2(texCoord.x, 1.0 - texCoord.y);
        }
    """
    
    // Fragment Shader - OpenGL ES 3.0 版本，简化版本以确保兼容性
    const val FRAGMENT_SHADER = """
        #version 300 es
        precision mediump float;

        #define PI 3.141592653589793
        #define disp 6.25

        in vec2 fragTexCoord;
        out vec4 outColor;

        uniform sampler2D backgroundTexture;
        uniform vec2 glassCenter;
        uniform float glassRadius;
        uniform float glassBlur;
        uniform vec4 glassColor;
        uniform vec2 lightPos;
        uniform vec2 contrl;
        uniform vec2 zoom;
        uniform vec2 resolution;
        uniform vec2 glassSize; // 新增：玻璃的宽度和高度
        uniform float glassCornerRadius; // 新增：玻璃的圆角半径
        
        // 精确实现原版的L3范数距离计算
        float distance_pow(vec2 p, float pw) {
            return pow(pow(abs(p.x), pw) + pow(abs(p.y), pw), 1.0/pw);
        }

        // 圆角矩形距离计算函数
        float roundedRectangleDistance(vec2 p, vec2 size, float radius) {
            vec2 d = abs(p) - size * 0.5 + radius;
            return length(max(d, 0.0)) + min(max(d.x, d.y), 0.0) - radius;
        }

        // 简单的文本渲染函数 - 渲染"嘿嘿嘿"
        float renderText(vec2 uv) {
            // 将坐标转换到文本空间 (0-1范围)
            vec2 textPos = (uv - glassCenter + glassSize * 0.5) / glassSize;

            // 只在玻璃内部显示文本
            if (textPos.x < 0.0 || textPos.x > 1.0 || textPos.y < 0.0 || textPos.y > 1.0) {
                return 0.0;
            }

            // 文本位置居中
            vec2 center = vec2(0.5, 0.5);
            vec2 p = textPos - center;

            float text = 0.0;

            // 渲染三个"嘿"字的简化版本
            // 第一个"嘿" (左边)
            vec2 p1 = p - vec2(-0.25, 0.0);
            if (abs(p1.x) < 0.08 && abs(p1.y) < 0.15) {
                // 简化的汉字笔画
                if ((abs(p1.x) < 0.02 && abs(p1.y) < 0.12) || // 竖笔
                    (abs(p1.y) < 0.02 && abs(p1.x) < 0.06) || // 横笔
                    (abs(p1.y - 0.06) < 0.02 && abs(p1.x) < 0.05)) { // 上横笔
                    text = 1.0;
                }
            }

            // 第二个"嘿" (中间)
            vec2 p2 = p - vec2(0.0, 0.0);
            if (abs(p2.x) < 0.08 && abs(p2.y) < 0.15) {
                if ((abs(p2.x) < 0.02 && abs(p2.y) < 0.12) ||
                    (abs(p2.y) < 0.02 && abs(p2.x) < 0.06) ||
                    (abs(p2.y - 0.06) < 0.02 && abs(p2.x) < 0.05)) {
                    text = 1.0;
                }
            }

            // 第三个"嘿" (右边)
            vec2 p3 = p - vec2(0.25, 0.0);
            if (abs(p3.x) < 0.08 && abs(p3.y) < 0.15) {
                if ((abs(p3.x) < 0.02 && abs(p3.y) < 0.12) ||
                    (abs(p3.y) < 0.02 && abs(p3.x) < 0.06) ||
                    (abs(p3.y - 0.06) < 0.02 && abs(p3.x) < 0.05)) {
                    text = 1.0;
                }
            }

            return text;
        }
        
        #define DITHER_SIZE 8
        #define GRAD 255.0
        
        // 简化的抖色矩阵
        float getDitherValue(vec2 coord) {
            int x = int(coord.x) % DITHER_SIZE;
            int y = int(coord.y) % DITHER_SIZE;
            // 简化的抖色计算
            return fract(sin(dot(coord, vec2(12.9898, 78.233))) * 43758.5453) * 0.5;
        }
        
        #define Pow 3.0
        #define A 1.75
        #define B 1.25
        #define C 2.0
        
        void main() {
            // 转换为屏幕坐标
            vec2 pixelCoord = vec2(fragTexCoord.x * resolution.x, fragTexCoord.y * resolution.y);
            float ditherValue = getDitherValue(pixelCoord);

            // 计算到玻璃中心的向量
            vec2 d = pixelCoord - glassCenter;

            // 圆角矩形距离计算 - 使用glassSize定义矩形尺寸和圆角半径
            float distance = roundedRectangleDistance(d, glassSize, glassCornerRadius);

            // 获取原始背景颜色
            vec3 back = texture(backgroundTexture, fragTexCoord).rgb;
            vec3 color = back;
            
            // 计算到光源的距离因子
            vec2 lightVec = pixelCoord - lightPos;
            float dt = clamp((abs(lightVec.x) + abs(lightVec.y)) / glassBlur, 0.0, 1.0);
            
            // 玻璃内部效果 - 对于矩形，当距离小于等于0时表示在矩形内部
            if (distance <= 0.0) {
                // 折射计算 - 适配矩形
                float blurDistance = max(glassSize.x, glassSize.y) * 0.1; // 模糊距离基于矩形尺寸
                float normalized = pow(clamp(max(0.0, distance + blurDistance) / blurDistance, 0.0, 1.0), A);
                float rel = 1.0 - pow(1.0 - pow(1.0 - normalized, B), C);
                float ref = rel * zoom.x;

                // 色散效果 - 适配矩形
                float avgSize = (glassSize.x + glassSize.y) * 0.5;
                float dsp = min(1.0, disp / avgSize * (zoom.y - ref));
                vec2 r_coord = glassCenter + d * (ref - dsp);
                vec2 g_coord = glassCenter + d * ref;
                vec2 b_coord = glassCenter + d * (ref + dsp);

                vec2 r_tex = vec2(r_coord.x / resolution.x, r_coord.y / resolution.y);
                vec2 g_tex = vec2(g_coord.x / resolution.x, g_coord.y / resolution.y);
                vec2 b_tex = vec2(b_coord.x / resolution.x, b_coord.y / resolution.y);

                // 确保纹理坐标在有效范围内
                r_tex = clamp(r_tex, 0.0, 1.0);
                g_tex = clamp(g_tex, 0.0, 1.0);
                b_tex = clamp(b_tex, 0.0, 1.0);

                float r = texture(backgroundTexture, r_tex).r;
                float g = texture(backgroundTexture, g_tex).g;
                float b = texture(backgroundTexture, b_tex).b;

                // 颜色叠加
                if (glassColor.a > 0.0) {
                    float col = (r * glassColor.r + g * glassColor.g + b * glassColor.b) / (glassColor.r + glassColor.g + glassColor.b);
                    col = pow(col, 1.0 / 2.2);
                    r += (col * glassColor.r - r) * glassColor.a;
                    g += (col * glassColor.g - g) * glassColor.a;
                    b += (col * glassColor.b - b) * glassColor.a;
                }

                // 内部阴影
                if(rel < 0.15){
                    float shad = 0.85 + rel;
                    r *= shad; g *= shad; b *= shad;
                }

                // 高光计算 - 适配矩形
                float num1 = clamp(distance + 2.25, 0.0, 1.0) * dt;
                float num2 = (1.0 - rel * pow(1.0 - rel, 2.0)) * pow(1.0 - (-distance) / blurDistance, 2.2) * dt;

                // 角度计算
                if(num1 > 0.0 || num2 > 0.0){
                    vec2 lightDir = lightPos - pixelCoord;
                    float angle = atan(d.y, d.x) - atan(lightDir.y, lightDir.x);
                    float ang1 = cos(angle);
                    float ang2 = cos(angle - PI);

                    // 应用高光
                    if(num1 > 0.0){
                        float high = num1 * max(0.16, ang1);
                        r *= 1.0 - high; g *= 1.0 - high; b *= 1.0 - high;
                        r += (1.0 - r) * high; g += (1.0 - g) * high; b += (1.0 - b) * high;
                    }

                    if(num2 > 0.0){
                        float high = num2 * pow(max(0.0, max(ang2 * 0.4, ang1 * 0.3)), 2.2);
                        r += (1.0 - r) * high; g += (1.0 - g) * high; b += (1.0 - b) * high;
                    }
                }

                color = vec3(r, g, b);

                // 在液体玻璃内部渲染"嘿嘿嘿"文本
                float textMask = renderText(pixelCoord);
                if (textMask > 0.0) {
                    // 文本颜色为白色，带有一些透明度混合
                    vec3 textColor = vec3(1.0, 1.0, 1.0);
                    color = mix(color, textColor, textMask * 0.8);
                }
            }

            // 外部阴影 - 适配矩形
            if (distance > -2.0 && distance <= 6.25) {
                float shad = (distance + 2.0) / 8.25;
                shad = clamp(shad, 0.0, 1.0);
                vec2 lightVec = pixelCoord - lightPos;
                float glassAngle = atan(d.y, d.x);
                float lightAngle = atan(lightVec.y, lightVec.x);
                float angle = glassAngle - lightAngle;

                shad = 1.0 - (1.0 - shad) * max(0.16, cos(angle)) * dt;
                shad = pow(shad, 1.0 / 2.2) * 0.5 + 0.5;

                float alp = clamp(-distance, 0.0, 1.0);
                back *= shad;
                color = mix(back, color, alp);
            }

            // 将颜色转换到0-255范围进行抖色计算
            outColor = vec4(
                floor(color.r * GRAD + ditherValue) / GRAD,
                floor(color.g * GRAD + ditherValue) / GRAD,
                floor(color.b * GRAD + ditherValue) / GRAD,
                1.0
            );
        }
    """
}
