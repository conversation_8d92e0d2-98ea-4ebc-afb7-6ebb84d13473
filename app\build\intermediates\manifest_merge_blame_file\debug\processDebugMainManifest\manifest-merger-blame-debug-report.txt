1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.zuijiji2"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
11-->C:\Users\<USER>\AndroidStudioProjects\zuijiji2\app\src\main\AndroidManifest.xml:5:5-80
11-->C:\Users\<USER>\AndroidStudioProjects\zuijiji2\app\src\main\AndroidManifest.xml:5:22-77
12    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
12-->C:\Users\<USER>\AndroidStudioProjects\zuijiji2\app\src\main\AndroidManifest.xml:6:5-76
12-->C:\Users\<USER>\AndroidStudioProjects\zuijiji2\app\src\main\AndroidManifest.xml:6:22-73
13
14    <!-- OpenGL ES 3.0 支持 -->
15    <uses-feature
15-->C:\Users\<USER>\AndroidStudioProjects\zuijiji2\app\src\main\AndroidManifest.xml:9:5-78
16        android:glEsVersion="0x00030000"
16-->C:\Users\<USER>\AndroidStudioProjects\zuijiji2\app\src\main\AndroidManifest.xml:9:19-51
17        android:required="true" />
17-->C:\Users\<USER>\AndroidStudioProjects\zuijiji2\app\src\main\AndroidManifest.xml:9:52-75
18
19    <permission
19-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef5e42c84125bc0e1eb078081f826ad1\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
20        android:name="com.example.zuijiji2.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
20-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef5e42c84125bc0e1eb078081f826ad1\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
21        android:protectionLevel="signature" />
21-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef5e42c84125bc0e1eb078081f826ad1\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
22
23    <uses-permission android:name="com.example.zuijiji2.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
23-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef5e42c84125bc0e1eb078081f826ad1\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
23-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef5e42c84125bc0e1eb078081f826ad1\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
24
25    <application
25-->C:\Users\<USER>\AndroidStudioProjects\zuijiji2\app\src\main\AndroidManifest.xml:11:5-32:19
26        android:allowBackup="true"
26-->C:\Users\<USER>\AndroidStudioProjects\zuijiji2\app\src\main\AndroidManifest.xml:12:9-35
27        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
27-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef5e42c84125bc0e1eb078081f826ad1\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
28        android:dataExtractionRules="@xml/data_extraction_rules"
28-->C:\Users\<USER>\AndroidStudioProjects\zuijiji2\app\src\main\AndroidManifest.xml:13:9-65
29        android:debuggable="true"
30        android:extractNativeLibs="false"
31        android:fullBackupContent="@xml/backup_rules"
31-->C:\Users\<USER>\AndroidStudioProjects\zuijiji2\app\src\main\AndroidManifest.xml:14:9-54
32        android:icon="@mipmap/ic_launcher"
32-->C:\Users\<USER>\AndroidStudioProjects\zuijiji2\app\src\main\AndroidManifest.xml:15:9-43
33        android:label="@string/app_name"
33-->C:\Users\<USER>\AndroidStudioProjects\zuijiji2\app\src\main\AndroidManifest.xml:16:9-41
34        android:roundIcon="@mipmap/ic_launcher_round"
34-->C:\Users\<USER>\AndroidStudioProjects\zuijiji2\app\src\main\AndroidManifest.xml:17:9-54
35        android:supportsRtl="true"
35-->C:\Users\<USER>\AndroidStudioProjects\zuijiji2\app\src\main\AndroidManifest.xml:18:9-35
36        android:testOnly="true"
37        android:theme="@style/Theme.Zuijiji2" >
37-->C:\Users\<USER>\AndroidStudioProjects\zuijiji2\app\src\main\AndroidManifest.xml:19:9-46
38        <activity
38-->C:\Users\<USER>\AndroidStudioProjects\zuijiji2\app\src\main\AndroidManifest.xml:21:9-31:20
39            android:name="com.example.zuijiji2.MainActivity"
39-->C:\Users\<USER>\AndroidStudioProjects\zuijiji2\app\src\main\AndroidManifest.xml:22:13-41
40            android:exported="true"
40-->C:\Users\<USER>\AndroidStudioProjects\zuijiji2\app\src\main\AndroidManifest.xml:23:13-36
41            android:label="@string/app_name"
41-->C:\Users\<USER>\AndroidStudioProjects\zuijiji2\app\src\main\AndroidManifest.xml:24:13-45
42            android:theme="@style/Theme.Zuijiji2" >
42-->C:\Users\<USER>\AndroidStudioProjects\zuijiji2\app\src\main\AndroidManifest.xml:25:13-50
43            <intent-filter>
43-->C:\Users\<USER>\AndroidStudioProjects\zuijiji2\app\src\main\AndroidManifest.xml:26:13-30:29
44                <action android:name="android.intent.action.MAIN" />
44-->C:\Users\<USER>\AndroidStudioProjects\zuijiji2\app\src\main\AndroidManifest.xml:27:17-69
44-->C:\Users\<USER>\AndroidStudioProjects\zuijiji2\app\src\main\AndroidManifest.xml:27:25-66
45
46                <category android:name="android.intent.category.LAUNCHER" />
46-->C:\Users\<USER>\AndroidStudioProjects\zuijiji2\app\src\main\AndroidManifest.xml:29:17-77
46-->C:\Users\<USER>\AndroidStudioProjects\zuijiji2\app\src\main\AndroidManifest.xml:29:27-74
47            </intent-filter>
48        </activity>
49        <activity
49-->[androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f38b5c402d23b30c2e2875c7c76f10e0\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
50            android:name="androidx.compose.ui.tooling.PreviewActivity"
50-->[androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f38b5c402d23b30c2e2875c7c76f10e0\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
51            android:exported="true" />
51-->[androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f38b5c402d23b30c2e2875c7c76f10e0\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
52        <activity
52-->[androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf0789ae4c448878dc74378ebdfb6958\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:23:9-25:39
53            android:name="androidx.activity.ComponentActivity"
53-->[androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf0789ae4c448878dc74378ebdfb6958\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:24:13-63
54            android:exported="true" />
54-->[androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf0789ae4c448878dc74378ebdfb6958\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:25:13-36
55
56        <provider
56-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\37f27ceb4a61ff296c8d63ebd994f71d\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
57            android:name="androidx.startup.InitializationProvider"
57-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\37f27ceb4a61ff296c8d63ebd994f71d\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
58            android:authorities="com.example.zuijiji2.androidx-startup"
58-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\37f27ceb4a61ff296c8d63ebd994f71d\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
59            android:exported="false" >
59-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\37f27ceb4a61ff296c8d63ebd994f71d\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
60            <meta-data
60-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\37f27ceb4a61ff296c8d63ebd994f71d\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
61                android:name="androidx.emoji2.text.EmojiCompatInitializer"
61-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\37f27ceb4a61ff296c8d63ebd994f71d\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
62                android:value="androidx.startup" />
62-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\37f27ceb4a61ff296c8d63ebd994f71d\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
63            <meta-data
63-->[androidx.lifecycle:lifecycle-process:2.9.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ba570cd6563dff0846737ee8ddb3896c\transformed\lifecycle-process-2.9.2\AndroidManifest.xml:29:13-31:52
64                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
64-->[androidx.lifecycle:lifecycle-process:2.9.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ba570cd6563dff0846737ee8ddb3896c\transformed\lifecycle-process-2.9.2\AndroidManifest.xml:30:17-78
65                android:value="androidx.startup" />
65-->[androidx.lifecycle:lifecycle-process:2.9.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ba570cd6563dff0846737ee8ddb3896c\transformed\lifecycle-process-2.9.2\AndroidManifest.xml:31:17-49
66            <meta-data
66-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d901372bf9c7b7edd04a2aa8ba9bd58b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
67                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
67-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d901372bf9c7b7edd04a2aa8ba9bd58b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
68                android:value="androidx.startup" />
68-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d901372bf9c7b7edd04a2aa8ba9bd58b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
69        </provider>
70
71        <receiver
71-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d901372bf9c7b7edd04a2aa8ba9bd58b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
72            android:name="androidx.profileinstaller.ProfileInstallReceiver"
72-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d901372bf9c7b7edd04a2aa8ba9bd58b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
73            android:directBootAware="false"
73-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d901372bf9c7b7edd04a2aa8ba9bd58b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
74            android:enabled="true"
74-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d901372bf9c7b7edd04a2aa8ba9bd58b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
75            android:exported="true"
75-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d901372bf9c7b7edd04a2aa8ba9bd58b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
76            android:permission="android.permission.DUMP" >
76-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d901372bf9c7b7edd04a2aa8ba9bd58b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
77            <intent-filter>
77-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d901372bf9c7b7edd04a2aa8ba9bd58b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
78                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
78-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d901372bf9c7b7edd04a2aa8ba9bd58b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
78-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d901372bf9c7b7edd04a2aa8ba9bd58b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
79            </intent-filter>
80            <intent-filter>
80-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d901372bf9c7b7edd04a2aa8ba9bd58b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
81                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
81-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d901372bf9c7b7edd04a2aa8ba9bd58b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
81-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d901372bf9c7b7edd04a2aa8ba9bd58b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
82            </intent-filter>
83            <intent-filter>
83-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d901372bf9c7b7edd04a2aa8ba9bd58b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
84                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
84-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d901372bf9c7b7edd04a2aa8ba9bd58b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
84-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d901372bf9c7b7edd04a2aa8ba9bd58b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
85            </intent-filter>
86            <intent-filter>
86-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d901372bf9c7b7edd04a2aa8ba9bd58b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
87                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
87-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d901372bf9c7b7edd04a2aa8ba9bd58b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
87-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d901372bf9c7b7edd04a2aa8ba9bd58b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
88            </intent-filter>
89        </receiver>
90    </application>
91
92</manifest>
