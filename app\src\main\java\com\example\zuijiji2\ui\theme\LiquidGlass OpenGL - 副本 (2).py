import pygame
import sys
import numpy as np
import math
import time
from OpenGL.GL import *
from OpenGL.GL.shaders import compileProgram, compileShader

from ctypes import windll
try: windll.shcore.SetProcessDpiAwareness(2)
except AttributeError:
    try: windll.user32.SetProcessDPIAware()
    except AttributeError: print("\033[33mWarning: Neither SetProcessDpiAwareness nor SetProcessDPIAware are available.")

# 在初始化Pygame前先截屏
def capture_screen():
    try:
        import win32gui
        import win32ui
        import win32con
        import win32api
        
        hdesktop = win32gui.GetDesktopWindow()
        width = win32api.GetSystemMetrics(win32con.SM_CXVIRTUALSCREEN)
        height = win32api.GetSystemMetrics(win32con.SM_CYVIRTUALSCREEN)
        left = win32api.GetSystemMetrics(win32con.SM_XVIRTUALSCREEN)
        top = win32api.GetSystemMetrics(win32con.SM_YVIRTUALSCREEN)
        
        desktop_dc = win32gui.GetWindowDC(hdesktop)
        img_dc = win32ui.CreateDCFromHandle(desktop_dc)
        mem_dc = img_dc.CreateCompatibleDC()
        
        screenshot = win32ui.CreateBitmap()
        screenshot.CreateCompatibleBitmap(img_dc, width, height)
        mem_dc.SelectObject(screenshot)
        mem_dc.BitBlt((0, 0), (width, height), img_dc, (left, top), win32con.SRCCOPY)
        
        bmpinfo = screenshot.GetInfo()
        bmpstr = screenshot.GetBitmapBits(True)
        
        img = pygame.image.frombuffer(bmpstr, (width, height), 'BGRA')
        
        mem_dc.DeleteDC()
        win32gui.DeleteObject(screenshot.GetHandle())
        img_dc.DeleteDC()
        win32gui.ReleaseDC(hdesktop, desktop_dc)
        return img
    except Exception as e:
        print(f"截屏失败: {e}")
        # 创建默认背景
        surface = pygame.Surface((800, 600), pygame.SRCALPHA)
        surface.fill((30, 30, 50, 255))
        pygame.draw.circle(surface, (100, 200, 255, 200), (400, 300), 200)
        pygame.draw.circle(surface, (255, 255, 255, 255), (400, 300), 100)
        return surface

# 先截屏再初始化Pygame
background = capture_screen()

# 着色器代码
vertex_shader = """
#version 330 core
layout(location = 0) in vec2 position;
layout(location = 1) in vec2 texCoord;
out vec2 fragTexCoord;
void main() {
    gl_Position = vec4(position, 0.0, 1.0);
    fragTexCoord = vec2(texCoord.x, texCoord.y);
}
"""

fragment_shader = """#version 330 core
#define PI 3.141592653589793
#define disp 6.25
in vec2 fragTexCoord;
out vec4 outColor;

uniform sampler2D backgroundTexture;
uniform vec2 glassCenter;
uniform float glassRadius;
uniform float glassBlur;
uniform vec4 glassColor;
uniform vec2 lightPos;
uniform vec2 contrl;
uniform vec2 zoom;
uniform vec2 resolution;

// 精确实现原版的L3范数距离计算
float r = 0.0, g = 0.0, b = 0.0;
float distance_pow(vec2 p, float pw) { return pow(pow(abs(p.x), pw) + pow(abs(p.y), pw), 1.0/pw); }
#define DITHER_SIZE 8
#define GRAD 255.0
const float ditherMatrix[DITHER_SIZE * DITHER_SIZE] = float[](
    0.0,      0.5,      0.125,    0.625,    0.03125,  0.53125,  0.15625,  0.65625,
    0.75,     0.25,     0.875,    0.375,    0.78125,  0.28125,  0.90625,  0.40625,
    0.1875,   0.6875,   0.0625,   0.5625,   0.21875,  0.71875,  0.09375,  0.59375,
    0.9375,   0.4375,   0.8125,   0.3125,   0.96875,  0.46875,  0.84375,  0.34375,
    0.046875, 0.546875, 0.171875, 0.671875, 0.015625, 0.515625, 0.140625, 0.640625,
    0.796875, 0.296875, 0.921875, 0.421875, 0.765625, 0.265625, 0.890625, 0.390625,
    0.234375, 0.734375, 0.109375, 0.609375, 0.203125, 0.703125, 0.078125, 0.578125,
    0.984375, 0.484375, 0.859375, 0.359375, 0.953125, 0.453125, 0.828125, 0.328125);

#define Pow 3.0
#define A 1.75
#define B 1.25
#define C 2.0

void main() {
    // 转换为屏幕坐标 (原点在左上角)
    vec2 pixelCoord = vec2(fragTexCoord.x * resolution.x, (1.0 - fragTexCoord.y) * resolution.y);
    float ditherValue = ditherMatrix[int(pixelCoord.x) % DITHER_SIZE * DITHER_SIZE + int(pixelCoord.y) % DITHER_SIZE];
    // 计算到玻璃中心的向量
    vec2 d = pixelCoord - glassCenter;
    // 使用L3范数计算距离
    float distance = distance_pow(d * vec2(1.0 / contrl.x, 1.0 / contrl.y), Pow);
    if (Pow < 0.75) distance += ditherValue - 0.5;
    // 获取原始背景颜色
    vec3 back = texture(backgroundTexture, fragTexCoord).rgb;
    vec3 color = back;
    // 计算到光源的距离因子
    vec2 lightVec = pixelCoord - lightPos;
    float dt = clamp((abs(lightVec.x) + abs(lightVec.y)) / glassBlur, 0.0, 1.0);
    // 玻璃内部效果
    if (distance <= glassRadius) {
        // 折射计算
            float normalized = pow(clamp(max(0.0, distance - glassRadius + glassBlur) / glassBlur, 0.0, 1.0), A);
            float rel = 1.0 - pow(1.0 - pow(1.0 - normalized, B), C);
        float ref = rel * zoom.x;
        int upsc = clamp(int(12 * abs(1.0 - ref) + ditherValue), 1, 8);
        if (upsc < 2) {
            float dsp = min(1.0, disp / glassRadius * (zoom.y - ref));
            vec2 r_coord = glassCenter + d * (ref - dsp);  // 计算各通道的采样位置
            vec2 g_coord = glassCenter + d * ref;
            vec2 b_coord = glassCenter + d * (ref + dsp);
            vec2 r_tex = vec2(r_coord.x / resolution.x, 1.0 - r_coord.y / resolution.y);  // 转换为纹理坐标
            vec2 g_tex = vec2(g_coord.x / resolution.x, 1.0 - g_coord.y / resolution.y);
            vec2 b_tex = vec2(b_coord.x / resolution.x, 1.0 - b_coord.y / resolution.y);
            r += texture(backgroundTexture, r_tex).r;  // 采样各通道
            g += texture(backgroundTexture, g_tex).g;
            b += texture(backgroundTexture, b_tex).b;
        } else {
            vec2 dx = d * vec2(1.0 / contrl.x, 1.0 / contrl.y); float dst, dsp;
            dst = distance_pow(dx + vec2(-0.5, -0.5), Pow);  // 左上计算
            normalized = pow(clamp(max(0.0, dst - glassRadius + glassBlur) / glassBlur, 0.0, 1.0), A);
            float ref_g0 = (1.0 - pow(1.0 - pow(1.0 - normalized, B), C)) * zoom.x;
            dsp = min(1.0, disp / glassRadius * (zoom.y - ref_g0)); float ref_r0 = ref_g0 - dsp, ref_b0 = ref_g0 + dsp;
            dst = distance_pow(dx + vec2(0.5, -0.5), Pow);  // 右上计算
            normalized = pow(clamp(max(0.0, dst - glassRadius + glassBlur) / glassBlur, 0.0, 1.0), A);
            float ref_g1 = (1.0 - pow(1.0 - pow(1.0 - normalized, B), C)) * zoom.x;
            dsp = min(1.0, disp / glassRadius * (zoom.y - ref_g1)); float ref_r1 = ref_g1 - dsp, ref_b1 = ref_g1 + dsp;
            dst = distance_pow(dx + vec2(-0.5, 0.5), Pow);  // 左下计算
            normalized = pow(clamp(max(0.0, dst - glassRadius + glassBlur) / glassBlur, 0.0, 1.0), A);
            float ref_g2 = (1.0 - pow(1.0 - pow(1.0 - normalized, B), C)) * zoom.x;
            dsp = min(1.0, disp / glassRadius * (zoom.y - ref_g2)); float ref_r2 = ref_g2 - dsp, ref_b2 = ref_g2 + dsp;
            dst = distance_pow(dx + vec2(0.5, 0.5), Pow);  // 右下计算
            normalized = pow(clamp(max(0.0, dst - glassRadius + glassBlur) / glassBlur, 0.0, 1.0), A);
            float ref_g3 = (1.0 - pow(1.0 - pow(1.0 - normalized, B), C)) * zoom.x;
            dsp = min(1.0, disp / glassRadius * (zoom.y - ref_g3)); float ref_r3 = ref_g3 - dsp, ref_b3 = ref_g3 + dsp;
            for (float i = 0; i < upsc; i += 1) {  // 循环采样
                for (float j = 0; j < upsc; j += 1) {
                    float inum = i / upsc, jnum = j / upsc;                  
                    vec2 r_coord = glassCenter + d * (ref_r0 + (ref_r1 - ref_r0) * inum + (ref_r2 - ref_r0) * jnum + (ref_r3 - ref_r0) * inum * jnum);
                    vec2 g_coord = glassCenter + d * (ref_g0 + (ref_g1 - ref_g0) * inum + (ref_g2 - ref_g0) * jnum + (ref_g3 - ref_g0) * inum * jnum);
                    vec2 b_coord = glassCenter + d * (ref_b0 + (ref_b1 - ref_b0) * inum + (ref_b2 - ref_b0) * jnum + (ref_b3 - ref_b0) * inum * jnum);
                    vec2 r_tex = vec2(r_coord.x / resolution.x, 1.0 - r_coord.y / resolution.y);  // 转换为纹理坐标
                    vec2 g_tex = vec2(g_coord.x / resolution.x, 1.0 - g_coord.y / resolution.y);
                    vec2 b_tex = vec2(b_coord.x / resolution.x, 1.0 - b_coord.y / resolution.y);
                    r += texture(backgroundTexture, r_tex).r; // 采样各通道
                    g += texture(backgroundTexture, g_tex).g;
                    b += texture(backgroundTexture, b_tex).b;
                }
            } r /= upsc * upsc; g /= upsc * upsc; b /= upsc * upsc;
        } // 颜色叠加
        if (glassColor.a > 0) {
            float col = (r * glassColor.r + g * glassColor.g + b * glassColor.b) / (glassColor.r + glassColor.g + glassColor.b);
            col = pow(col, 1 / 2.2);
            r += (col * glassColor.r - r) * glassColor.a;
            g += (col * glassColor.g - g) * glassColor.a;
            b += (col * glassColor.b - b) * glassColor.a;
        } // 内部阴影
        if(rel < 0.15){
            float shad = 0.85 + rel;
            r *= shad; g *= shad; b *= shad;
        } // 高光计算
        float num1 = clamp(distance - glassRadius + 2.25, 0.0, 1.0) * dt;
        float num2 = (1 - rel * pow(1 - rel, 2)) * pow(1 - (glassRadius - distance) / glassBlur, 2.2) * dt;
        // 角度计算
        float ang1 = 0.0;
        float ang2 = 0.0;
        if(num1 > 0.0 || num2 > 0.0){
            // 计算当前点到光源的角度
            vec2 lightDir = lightPos - pixelCoord;
            // 计算角度差
            float angle = atan(d.y, d.x) - atan(lightDir.y, lightDir.x);
            // 计算角度因子
            ang1 = cos(angle);
            ang2 = cos(angle - PI);
        } // 应用num1高光
        if(num1 > 0.0){
            float high = num1 * max(0.16, ang1);
            r *= 1.0 - high; g *= 1.0 - high; b *= 1.0 - high;
            r += (1.0 - r) * high; g += (1.0 - g) * high; b += (1.0 - b) * high;
        } // 应用num2高光 
        if(num2 > 0.0){
            float high = num2 * pow(max(0.0, max(ang2 * 0.4, ang1 * 0.3)), 2.2);
            r += (1.0 - r) * high; g += (1.0 - g) * high; b += (1.0 - b) * high;
        } color = vec3(r, g, b);
    } if (distance > glassRadius - 2 && distance <= glassRadius + 6.25) {
        float shad = (distance - glassRadius) / 6.25;
        shad = clamp(shad, 0.0, 1.0);
        vec2 lightVec = pixelCoord - lightPos;
        float glassAngle = atan(d.y, d.x);
        float lightAngle = atan(lightVec.y, lightVec.x);
        float angle = glassAngle - lightAngle;
        // 与Python版一致的阴影计算
        shad = 1.0 - (1.0 - shad) * max(0.16, cos(angle)) * dt;
        shad = pow(shad, 1.0 / 2.2) * 0.5 + 0.5;
        // 透明度混合系数
        float alp = clamp(glassRadius - distance, 0.0, 1.0);
        back *= shad;
        color = mix(back, color, alp);
    } // 将颜色转换到0-255范围进行抖色计算
    outColor = vec4(
        floor(color.r * GRAD + ditherValue) / GRAD,
        floor(color.g * GRAD + ditherValue) / GRAD,
        floor(color.b * GRAD + ditherValue) / GRAD,
        1.0
    );
}"""

def create_shader_program():
    return compileProgram(
        compileShader(vertex_shader, GL_VERTEX_SHADER),
        compileShader(fragment_shader, GL_FRAGMENT_SHADER)
    )

def setup_quad():
    vertices = np.array([
        # 位置      # 纹理坐标
        -1.0, -1.0,  0.0, 0.0,  # 左下
         1.0, -1.0,  1.0, 0.0,  # 右下
         1.0,  1.0,  1.0, 1.0,  # 右上
        -1.0,  1.0,  0.0, 1.0   # 左上
    ], dtype=np.float32)
    
    indices = np.array([0, 1, 2, 2, 3, 0], dtype=np.uint32)
    
    vao = glGenVertexArrays(1)
    glBindVertexArray(vao)
    
    vbo = glGenBuffers(1)
    glBindBuffer(GL_ARRAY_BUFFER, vbo)
    glBufferData(GL_ARRAY_BUFFER, vertices.nbytes, vertices, GL_STATIC_DRAW)
    
    ebo = glGenBuffers(1)
    glBindBuffer(GL_ELEMENT_ARRAY_BUFFER, ebo)
    glBufferData(GL_ELEMENT_ARRAY_BUFFER, indices.nbytes, indices, GL_STATIC_DRAW)
    
    # 位置属性
    glVertexAttribPointer(0, 2, GL_FLOAT, GL_FALSE, 4 * sizeof(GLfloat), ctypes.c_void_p(0))
    glEnableVertexAttribArray(0)
    
    # 纹理坐标属性
    glVertexAttribPointer(1, 2, GL_FLOAT, GL_FALSE, 4 * sizeof(GLfloat), ctypes.c_void_p(2 * sizeof(GLfloat)))
    glEnableVertexAttribArray(1)
    
    glBindVertexArray(0)
    return vao, vbo, ebo

def texture_from_surface(surface):
    width, height = surface.get_size()
    # 转换为RGBA格式
    if surface.get_bytesize() != 4:
        surface = surface.convert_alpha()
    
    data = pygame.image.tobytes(surface, "RGBA", True)
    
    texture = glGenTextures(1)
    glBindTexture(GL_TEXTURE_2D, texture)
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_WRAP_S, GL_CLAMP_TO_EDGE)
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_WRAP_T, GL_CLAMP_TO_EDGE)
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MIN_FILTER, GL_LINEAR)
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MAG_FILTER, GL_LINEAR)
    glTexImage2D(GL_TEXTURE_2D, 0, GL_RGBA, width, height, 0, GL_RGBA, GL_UNSIGNED_BYTE, data)
    
    return texture

# 初始化Pygame
pygame.init()

# 获取屏幕尺寸
WIDTH, HEIGHT = pygame.display.get_desktop_sizes()[0]

# 使用原版标志创建窗口
screen = pygame.display.set_mode((WIDTH, HEIGHT),
                                 pygame.FULLSCREEN | pygame.OPENGL | pygame.HWSURFACE | pygame.HWACCEL | pygame.HWPALETTE | pygame.DOUBLEBUF,
                                 vsync=1)
pygame.display.set_caption("Liquid Glass 模拟效果")

# 调整背景尺寸
background = pygame.transform.smoothscale(background, (WIDTH, HEIGHT))

# 初始化OpenGL
glViewport(0, 0, WIDTH, HEIGHT)
glEnable(GL_BLEND)
glBlendFunc(GL_SRC_ALPHA, GL_ONE_MINUS_SRC_ALPHA)

# 创建着色器程序
shader_program = create_shader_program()

# 设置全屏四边形
vao, vbo, ebo = setup_quad()

# 创建背景纹理
background_texture = texture_from_surface(background)

# 玻璃参数
glass_radius = WIDTH * 0.08
glass_center = (WIDTH / 2, HEIGHT / 2)
disp = 8.25

# 计时器
last_time = time.time()
glass_rad = glass_radius
mouse = glass_center
running = True
frm = 0

glass_center_loc = glGetUniformLocation(shader_program, "glassCenter")
glass_radius_loc = glGetUniformLocation(shader_program, "glassRadius")
glass_blur_loc = glGetUniformLocation(shader_program, "glassBlur")
color_loc = glGetUniformLocation(shader_program, "glassColor")
light_loc = glGetUniformLocation(shader_program, "lightPos")
contrl_loc = glGetUniformLocation(shader_program, "contrl")
zoom_loc = glGetUniformLocation(shader_program, "zoom")
res_loc = glGetUniformLocation(shader_program, "resolution")
bgtexture_loc = glGetUniformLocation(shader_program, "backgroundTexture")

# 主循环
while running:
    current_time = time.time()
    dt = current_time - last_time
    last_time = current_time
    frm += 1
    
    for event in pygame.event.get():
        if event.type == pygame.QUIT:
            running = False
        elif event.type == pygame.KEYDOWN:
            if event.key == pygame.K_ESCAPE:
                running = False
        elif event.type == pygame.MOUSEBUTTONDOWN:
            if event.button == 4:  # 滚轮上
                glass_rad *= 1.075
            elif event.button == 5:  # 滚轮下
                if glass_rad > 8.0:
                    glass_rad /= 1.075
        elif event.type == pygame.MOUSEMOTION:
            # 中键按下时显示光标
            pygame.mouse.set_visible(pygame.mouse.get_pressed()[1])
            mouse = event.pos
    
    # 更新玻璃位置和大小
    num = max(0, min(0.96, 6.4 * dt))
    pressed = any(pygame.mouse.get_pressed())
    glass_center = (
        glass_center[0] + (mouse[0] - glass_center[0]) * num,
        glass_center[1] + (mouse[1] - glass_center[1]) * num
    )
    glass_rad = min(WIDTH * 0.5 / (0.96 if pressed else 1.0), HEIGHT * 0.5 / (0.96 if pressed else 1.0), glass_rad)
    glass_radius = glass_radius + (glass_rad * (0.96 if pressed else 1.0) - glass_radius) * num
    glass_center = (max(glass_radius, min(WIDTH - glass_radius, glass_center[0])),
                    max(glass_radius, min(HEIGHT - glass_radius, glass_center[1])))
    glass_blur = glass_radius * 0.5
    
    # 动态变形参数
    n1, n2 = 0.005, 1.5
    zx = math.sin(current_time * n2) * n1 + 1
    zy = math.cos(current_time * n2) * n1 + 1
    
    # 清除屏幕 - 使用黑色背景
    glClearColor(0.0, 0.0, 0.0, 1.0)
    glClear(GL_COLOR_BUFFER_BIT)
    
    # 使用着色器
    glUseProgram(shader_program)
    
    # 设置uniform变量
    glUniform2f(glass_center_loc, glass_center[0], glass_center[1])
    
    glUniform1f(glass_radius_loc, glass_radius)
    glUniform1f(glass_blur_loc, glass_blur)
    
    # 玻璃颜色 (蓝色)
    glUniform4f(color_loc, 51 / 255.0, 102 / 255.0, 1.0, 0.0)  # RGBA
    
    # 光源位置 (左上角)
    glUniform2f(light_loc, WIDTH * 0.25, HEIGHT * 0.25)
    
    glUniform2f(contrl_loc, zx, zy)
    glUniform2f(zoom_loc, 1.0, 1.0)
    glUniform2f(res_loc, WIDTH, HEIGHT)
    
    # 绑定背景纹理
    glActiveTexture(GL_TEXTURE0)
    glBindTexture(GL_TEXTURE_2D, background_texture)
    glUniform1i(bgtexture_loc, 0)
    
    # 绘制全屏四边形
    glBindVertexArray(vao)
    glDrawElements(GL_TRIANGLES, 6, GL_UNSIGNED_INT, None)
    glBindVertexArray(0)
    
    # 更新显示
    pygame.display.flip()

# 清理资源
glDeleteProgram(shader_program)
glDeleteVertexArrays(1, [vao])
glDeleteBuffers(1, [vbo])
glDeleteBuffers(1, [ebo])
glDeleteTextures(1, [background_texture])
pygame.quit()
sys.exit()