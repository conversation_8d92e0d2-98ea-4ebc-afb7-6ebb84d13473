package com.example.zuijiji2.opengl

import android.content.Context
import android.graphics.Bitmap
import android.opengl.GLSurfaceView
import android.util.AttributeSet
import android.util.Log
import android.view.MotionEvent
import android.view.ScaleGestureDetector
import kotlin.math.max
import kotlin.math.min

class LiquidGlassView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null
) : GLSurfaceView(context, attrs) {
    
    private val TAG = "LiquidGlassView"
    
    private val renderer: LiquidGlassRenderer
    private val scaleGestureDetector: ScaleGestureDetector
    
    // 触摸和缩放状态
    private var lastTouchX = 0f
    private var lastTouchY = 0f
    private var scaleFactor = 1.0f
    private var isScaling = false
    private var scaleStartX = 0f
    private var scaleStartY = 0f
    private var isDragging = false
    private var dragStartX = 0f
    private var dragStartY = 0f

    // 液体玻璃的当前位置
    private var glassCurrentX = 0f
    private var glassCurrentY = 0f
    private var glassInitialized = false
    
    init {
        // 设置OpenGL ES 3.0
        setEGLContextClientVersion(3)
        
        // 创建渲染器
        renderer = LiquidGlassRenderer()
        setRenderer(renderer)
        
        // 设置渲染模式为连续渲染（用于动画）
        renderMode = RENDERMODE_CONTINUOUSLY
        
        // 创建缩放手势检测器
        scaleGestureDetector = ScaleGestureDetector(context, ScaleListener())
        
        Log.d(TAG, "LiquidGlassView initialized")
    }
    
    /**
     * 设置背景位图
     */
    fun setBackgroundBitmap(bitmap: Bitmap) {
        Log.d(TAG, "Setting background bitmap: ${bitmap.width}x${bitmap.height}")
        queueEvent {
            renderer.setBackgroundBitmap(bitmap)
        }
    }

    /**
     * 设置自定义玻璃尺寸
     */
    fun setCustomGlassSize(width: Float, height: Float) {
        Log.d(TAG, "Setting custom glass size: ${width}x${height}")
        queueEvent {
            renderer.setCustomGlassSize(width, height)
        }
    }
    
    /**
     * 处理触摸事件
     */
    override fun onTouchEvent(event: MotionEvent): Boolean {
        // 首先让缩放手势检测器处理事件
        val scaleHandled = scaleGestureDetector.onTouchEvent(event)

        // 如果是多点触摸，直接返回，避免单点触摸逻辑干扰
        if (event.pointerCount > 1) {
            return scaleHandled
        }

        when (event.action) {
            MotionEvent.ACTION_DOWN -> {
                lastTouchX = event.x
                lastTouchY = event.y
                dragStartX = event.x
                dragStartY = event.y
                isDragging = false

                // 如果是第一次触摸，初始化玻璃位置为屏幕中心
                if (!glassInitialized) {
                    glassCurrentX = width / 2f
                    glassCurrentY = height / 2f
                    glassInitialized = true
                    renderer.updateGlassCenter(glassCurrentX, glassCurrentY)
                    requestRender()
                }

                Log.d(TAG, "Touch down at (${event.x}, ${event.y}), glass at (${glassCurrentX}, ${glassCurrentY})")
                return true
            }

            MotionEvent.ACTION_MOVE -> {
                // 只有在单点触摸且不在缩放状态时才移动玻璃
                if (!isScaling && event.pointerCount == 1) {
                    val deltaX = event.x - lastTouchX
                    val deltaY = event.y - lastTouchY

                    // 检测是否开始拖动（移动距离超过阈值）
                    if (!isDragging) {
                        val totalDelta = kotlin.math.sqrt((event.x - dragStartX) * (event.x - dragStartX) +
                                                         (event.y - dragStartY) * (event.y - dragStartY))
                        if (totalDelta > 20) { // 20像素的拖动阈值
                            isDragging = true
                            Log.d(TAG, "Started dragging")
                        }
                    }

                    // 只有在拖动状态下才移动玻璃，使用相对偏移
                    if (isDragging) {
                        glassCurrentX += deltaX
                        glassCurrentY += deltaY

                        // 限制玻璃位置在屏幕范围内
                        glassCurrentX = glassCurrentX.coerceIn(0f, width.toFloat())
                        glassCurrentY = glassCurrentY.coerceIn(0f, height.toFloat())

                        renderer.updateGlassCenter(glassCurrentX, glassCurrentY)
                        requestRender()
                        Log.d(TAG, "Dragging glass to (${glassCurrentX}, ${glassCurrentY})")
                    }
                }

                lastTouchX = event.x
                lastTouchY = event.y
                return true
            }

            MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                // 确保状态重置
                if (event.pointerCount <= 1) {
                    isScaling = false
                    isDragging = false
                }
                Log.d(TAG, "Touch up, isScaling: $isScaling, isDragging: $isDragging")
                return true
            }
        }
        
        return super.onTouchEvent(event)
    }
    
    /**
     * 缩放手势监听器
     */
    private inner class ScaleListener : ScaleGestureDetector.SimpleOnScaleGestureListener() {
        
        override fun onScaleBegin(detector: ScaleGestureDetector): Boolean {
            isScaling = true
            isDragging = false // 开始缩放时停止拖动
            // 记录缩放开始时的中心位置，保持玻璃位置不变
            scaleStartX = detector.focusX
            scaleStartY = detector.focusY

            // 如果玻璃还没有初始化，使用缩放中心作为玻璃位置
            if (!glassInitialized) {
                glassCurrentX = scaleStartX
                glassCurrentY = scaleStartY
                glassInitialized = true
                renderer.updateGlassCenter(glassCurrentX, glassCurrentY)
            }

            Log.d(TAG, "Scale begin at (${scaleStartX}, ${scaleStartY})")
            return true
        }
        
        override fun onScale(detector: ScaleGestureDetector): Boolean {
            scaleFactor *= detector.scaleFactor

            // 限制缩放范围
            scaleFactor = max(0.5f, min(scaleFactor, 3.0f))

            // 更新玻璃半径，不使用queueEvent以减少延迟
            renderer.updateGlassRadius(scaleFactor)
            requestRender()

            Log.d(TAG, "Scale factor: $scaleFactor")
            return true
        }
        
        override fun onScaleEnd(detector: ScaleGestureDetector) {
            isScaling = false
            isDragging = false // 缩放结束时重置拖动状态
            Log.d(TAG, "Scale end")
        }
    }
    
    /**
     * 暂停渲染
     */
    override fun onPause() {
        super.onPause()
        Log.d(TAG, "onPause")
    }
    
    /**
     * 恢复渲染
     */
    override fun onResume() {
        super.onResume()
        Log.d(TAG, "onResume")
    }
    
    /**
     * 清理资源
     */
    fun cleanup() {
        queueEvent {
            renderer.cleanup()
        }
    }
}
