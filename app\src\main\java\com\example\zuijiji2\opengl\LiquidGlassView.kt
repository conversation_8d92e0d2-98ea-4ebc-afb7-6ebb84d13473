package com.example.zuijiji2.opengl

import android.content.Context
import android.graphics.Bitmap
import android.opengl.GLSurfaceView
import android.util.AttributeSet
import android.util.Log
import android.view.MotionEvent
import android.view.ScaleGestureDetector
import kotlin.math.max
import kotlin.math.min

class LiquidGlassView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null
) : GLSurfaceView(context, attrs) {
    
    private val TAG = "LiquidGlassView"
    
    private val renderer: LiquidGlassRenderer
    private val scaleGestureDetector: ScaleGestureDetector
    
    // 触摸和缩放状态
    private var lastTouchX = 0f
    private var lastTouchY = 0f
    private var scaleFactor = 1.0f
    private var isScaling = false
    private var scaleStartX = 0f
    private var scaleStartY = 0f
    
    init {
        // 设置OpenGL ES 3.0
        setEGLContextClientVersion(3)
        
        // 创建渲染器
        renderer = LiquidGlassRenderer()
        setRenderer(renderer)
        
        // 设置渲染模式为连续渲染（用于动画）
        renderMode = RENDERMODE_CONTINUOUSLY
        
        // 创建缩放手势检测器
        scaleGestureDetector = ScaleGestureDetector(context, ScaleListener())
        
        Log.d(TAG, "LiquidGlassView initialized")
    }
    
    /**
     * 设置背景位图
     */
    fun setBackgroundBitmap(bitmap: Bitmap) {
        Log.d(TAG, "Setting background bitmap: ${bitmap.width}x${bitmap.height}")
        queueEvent {
            renderer.setBackgroundBitmap(bitmap)
        }
    }
    
    /**
     * 处理触摸事件
     */
    override fun onTouchEvent(event: MotionEvent): Boolean {
        // 首先让缩放手势检测器处理事件
        val scaleHandled = scaleGestureDetector.onTouchEvent(event)

        // 如果是多点触摸，直接返回，避免单点触摸逻辑干扰
        if (event.pointerCount > 1) {
            return scaleHandled
        }

        when (event.action) {
            MotionEvent.ACTION_DOWN -> {
                lastTouchX = event.x
                lastTouchY = event.y

                // 立即更新玻璃中心位置，不使用queueEvent
                renderer.updateGlassCenter(event.x, event.y)
                requestRender() // 强制立即渲染

                Log.d(TAG, "Touch down at (${event.x}, ${event.y})")
                return true
            }
            
            MotionEvent.ACTION_MOVE -> {
                // 只有在单点触摸且不在缩放状态时才移动玻璃
                if (!isScaling && event.pointerCount == 1) {
                    renderer.updateGlassCenter(event.x, event.y)
                    requestRender()
                    Log.d(TAG, "Moving glass to (${event.x}, ${event.y})")
                }

                lastTouchX = event.x
                lastTouchY = event.y
                return true
            }
            
            MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                // 确保缩放状态重置
                if (event.pointerCount <= 1) {
                    isScaling = false
                }
                Log.d(TAG, "Touch up, isScaling: $isScaling")
                return true
            }
        }
        
        return super.onTouchEvent(event)
    }
    
    /**
     * 缩放手势监听器
     */
    private inner class ScaleListener : ScaleGestureDetector.SimpleOnScaleGestureListener() {
        
        override fun onScaleBegin(detector: ScaleGestureDetector): Boolean {
            isScaling = true
            // 记录缩放开始时的中心位置，保持玻璃位置不变
            scaleStartX = detector.focusX
            scaleStartY = detector.focusY
            Log.d(TAG, "Scale begin at (${scaleStartX}, ${scaleStartY})")
            return true
        }
        
        override fun onScale(detector: ScaleGestureDetector): Boolean {
            scaleFactor *= detector.scaleFactor

            // 限制缩放范围
            scaleFactor = max(0.5f, min(scaleFactor, 3.0f))

            // 更新玻璃半径，不使用queueEvent以减少延迟
            renderer.updateGlassRadius(scaleFactor)
            requestRender()

            Log.d(TAG, "Scale factor: $scaleFactor")
            return true
        }
        
        override fun onScaleEnd(detector: ScaleGestureDetector) {
            isScaling = false
            Log.d(TAG, "Scale end")
        }
    }
    
    /**
     * 暂停渲染
     */
    override fun onPause() {
        super.onPause()
        Log.d(TAG, "onPause")
    }
    
    /**
     * 恢复渲染
     */
    override fun onResume() {
        super.onResume()
        Log.d(TAG, "onResume")
    }
    
    /**
     * 清理资源
     */
    fun cleanup() {
        queueEvent {
            renderer.cleanup()
        }
    }
}
