  Activity android.app  
MainScreen android.app.Activity  
Zuijiji2Theme android.app.Activity  enableEdgeToEdge android.app.Activity  onCreate android.app.Activity  
setContent android.app.Activity  Context android.content  
MainScreen android.content.Context  
Zuijiji2Theme android.content.Context  enableEdgeToEdge android.content.Context  
setContent android.content.Context  
MainScreen android.content.ContextWrapper  
Zuijiji2Theme android.content.ContextWrapper  enableEdgeToEdge android.content.ContextWrapper  
setContent android.content.ContextWrapper  Uri android.net  let android.net.Uri  Build 
android.os  Bundle 
android.os  SDK_INT android.os.Build.VERSION  S android.os.Build.VERSION_CODES  
MainScreen  android.view.ContextThemeWrapper  
Zuijiji2Theme  android.view.ContextThemeWrapper  enableEdgeToEdge  android.view.ContextThemeWrapper  
setContent  android.view.ContextThemeWrapper  ComponentActivity androidx.activity  enableEdgeToEdge androidx.activity  Bundle #androidx.activity.ComponentActivity  	Companion #androidx.activity.ComponentActivity  
MainScreen #androidx.activity.ComponentActivity  
Zuijiji2Theme #androidx.activity.ComponentActivity  enableEdgeToEdge #androidx.activity.ComponentActivity  onCreate #androidx.activity.ComponentActivity  
setContent #androidx.activity.ComponentActivity  
MainScreen -androidx.activity.ComponentActivity.Companion  
Zuijiji2Theme -androidx.activity.ComponentActivity.Companion  enableEdgeToEdge -androidx.activity.ComponentActivity.Companion  
setContent -androidx.activity.ComponentActivity.Companion  ManagedActivityResultLauncher androidx.activity.compose  !rememberLauncherForActivityResult androidx.activity.compose  
setContent androidx.activity.compose  launch 7androidx.activity.compose.ManagedActivityResultLauncher  ActivityResultContracts !androidx.activity.result.contract  
GetContent 9androidx.activity.result.contract.ActivityResultContracts  Canvas androidx.compose.foundation  Image androidx.compose.foundation  
background androidx.compose.foundation  isSystemInDarkTheme androidx.compose.foundation  detectDragGestures $androidx.compose.foundation.gestures  ActivityResultContracts "androidx.compose.foundation.layout  	Alignment "androidx.compose.foundation.layout  Box "androidx.compose.foundation.layout  BoxScope "androidx.compose.foundation.layout  Bundle "androidx.compose.foundation.layout  Canvas "androidx.compose.foundation.layout  CircleShape "androidx.compose.foundation.layout  Color "androidx.compose.foundation.layout  ComponentActivity "androidx.compose.foundation.layout  
Composable "androidx.compose.foundation.layout  ContentScale "androidx.compose.foundation.layout  FloatingActionButton "androidx.compose.foundation.layout  Icon "androidx.compose.foundation.layout  Icons "androidx.compose.foundation.layout  Image "androidx.compose.foundation.layout  
MagnifierView "androidx.compose.foundation.layout  
MainScreen "androidx.compose.foundation.layout  Modifier "androidx.compose.foundation.layout  Offset "androidx.compose.foundation.layout  Preview "androidx.compose.foundation.layout  Unit "androidx.compose.foundation.layout  Uri "androidx.compose.foundation.layout  
Zuijiji2Theme "androidx.compose.foundation.layout  align "androidx.compose.foundation.layout  androidx "androidx.compose.foundation.layout  
background "androidx.compose.foundation.layout  clip "androidx.compose.foundation.layout  fillMaxSize "androidx.compose.foundation.layout  getValue "androidx.compose.foundation.layout  let "androidx.compose.foundation.layout  mutableStateOf "androidx.compose.foundation.layout  offset "androidx.compose.foundation.layout  padding "androidx.compose.foundation.layout  provideDelegate "androidx.compose.foundation.layout  remember "androidx.compose.foundation.layout  rememberAsyncImagePainter "androidx.compose.foundation.layout  setValue "androidx.compose.foundation.layout  size "androidx.compose.foundation.layout  with "androidx.compose.foundation.layout  Add +androidx.compose.foundation.layout.BoxScope  	Alignment +androidx.compose.foundation.layout.BoxScope  Box +androidx.compose.foundation.layout.BoxScope  Canvas +androidx.compose.foundation.layout.BoxScope  CircleShape +androidx.compose.foundation.layout.BoxScope  Color +androidx.compose.foundation.layout.BoxScope  ContentScale +androidx.compose.foundation.layout.BoxScope  FloatingActionButton +androidx.compose.foundation.layout.BoxScope  Icon +androidx.compose.foundation.layout.BoxScope  Icons +androidx.compose.foundation.layout.BoxScope  Image +androidx.compose.foundation.layout.BoxScope  
MagnifierView +androidx.compose.foundation.layout.BoxScope  Modifier +androidx.compose.foundation.layout.BoxScope  Offset +androidx.compose.foundation.layout.BoxScope  Search +androidx.compose.foundation.layout.BoxScope  align +androidx.compose.foundation.layout.BoxScope  androidx +androidx.compose.foundation.layout.BoxScope  
background +androidx.compose.foundation.layout.BoxScope  clip +androidx.compose.foundation.layout.BoxScope  dp +androidx.compose.foundation.layout.BoxScope  fillMaxSize +androidx.compose.foundation.layout.BoxScope  let +androidx.compose.foundation.layout.BoxScope  offset +androidx.compose.foundation.layout.BoxScope  padding +androidx.compose.foundation.layout.BoxScope  rememberAsyncImagePainter +androidx.compose.foundation.layout.BoxScope  size +androidx.compose.foundation.layout.BoxScope  with +androidx.compose.foundation.layout.BoxScope  CircleShape !androidx.compose.foundation.shape  RoundedCornerShape !androidx.compose.foundation.shape  Icons androidx.compose.material.icons  Default %androidx.compose.material.icons.Icons  Filled %androidx.compose.material.icons.Icons  Add ,androidx.compose.material.icons.Icons.Filled  Search ,androidx.compose.material.icons.Icons.Filled  Add &androidx.compose.material.icons.filled  Search &androidx.compose.material.icons.filled  ActivityResultContracts androidx.compose.material3  	Alignment androidx.compose.material3  Box androidx.compose.material3  Bundle androidx.compose.material3  Canvas androidx.compose.material3  CircleShape androidx.compose.material3  Color androidx.compose.material3  ColorScheme androidx.compose.material3  ComponentActivity androidx.compose.material3  
Composable androidx.compose.material3  ContentScale androidx.compose.material3  FloatingActionButton androidx.compose.material3  Icon androidx.compose.material3  Icons androidx.compose.material3  Image androidx.compose.material3  
MagnifierView androidx.compose.material3  
MainScreen androidx.compose.material3  
MaterialTheme androidx.compose.material3  Modifier androidx.compose.material3  Offset androidx.compose.material3  Preview androidx.compose.material3  
Typography androidx.compose.material3  Unit androidx.compose.material3  Uri androidx.compose.material3  
Zuijiji2Theme androidx.compose.material3  align androidx.compose.material3  androidx androidx.compose.material3  
background androidx.compose.material3  clip androidx.compose.material3  darkColorScheme androidx.compose.material3  dynamicDarkColorScheme androidx.compose.material3  dynamicLightColorScheme androidx.compose.material3  fillMaxSize androidx.compose.material3  getValue androidx.compose.material3  let androidx.compose.material3  lightColorScheme androidx.compose.material3  mutableStateOf androidx.compose.material3  offset androidx.compose.material3  padding androidx.compose.material3  provideDelegate androidx.compose.material3  remember androidx.compose.material3  rememberAsyncImagePainter androidx.compose.material3  setValue androidx.compose.material3  size androidx.compose.material3  with androidx.compose.material3  ActivityResultContracts androidx.compose.runtime  	Alignment androidx.compose.runtime  Box androidx.compose.runtime  Bundle androidx.compose.runtime  Canvas androidx.compose.runtime  CircleShape androidx.compose.runtime  Color androidx.compose.runtime  ComponentActivity androidx.compose.runtime  
Composable androidx.compose.runtime  ContentScale androidx.compose.runtime  FloatingActionButton androidx.compose.runtime  Icon androidx.compose.runtime  Icons androidx.compose.runtime  Image androidx.compose.runtime  
MagnifierView androidx.compose.runtime  
MainScreen androidx.compose.runtime  Modifier androidx.compose.runtime  MutableState androidx.compose.runtime  Offset androidx.compose.runtime  Preview androidx.compose.runtime  ProvidableCompositionLocal androidx.compose.runtime  Unit androidx.compose.runtime  Uri androidx.compose.runtime  
Zuijiji2Theme androidx.compose.runtime  align androidx.compose.runtime  androidx androidx.compose.runtime  
background androidx.compose.runtime  clip androidx.compose.runtime  fillMaxSize androidx.compose.runtime  getValue androidx.compose.runtime  let androidx.compose.runtime  mutableStateOf androidx.compose.runtime  offset androidx.compose.runtime  padding androidx.compose.runtime  provideDelegate androidx.compose.runtime  remember androidx.compose.runtime  rememberAsyncImagePainter androidx.compose.runtime  setValue androidx.compose.runtime  size androidx.compose.runtime  with androidx.compose.runtime  
getCurrent )androidx.compose.runtime.CompositionLocal  setValue %androidx.compose.runtime.MutableState  current 3androidx.compose.runtime.ProvidableCompositionLocal  ComposableFunction0 !androidx.compose.runtime.internal  ComposableFunction1 !androidx.compose.runtime.internal  	Alignment androidx.compose.ui  Modifier androidx.compose.ui  	BottomEnd androidx.compose.ui.Alignment  BottomStart androidx.compose.ui.Alignment  	Companion androidx.compose.ui.Alignment  	BottomEnd 'androidx.compose.ui.Alignment.Companion  BottomStart 'androidx.compose.ui.Alignment.Companion  	Companion androidx.compose.ui.Modifier  align androidx.compose.ui.Modifier  
background androidx.compose.ui.Modifier  clip androidx.compose.ui.Modifier  fillMaxSize androidx.compose.ui.Modifier  offset androidx.compose.ui.Modifier  padding androidx.compose.ui.Modifier  pointerInput androidx.compose.ui.Modifier  size androidx.compose.ui.Modifier  align &androidx.compose.ui.Modifier.Companion  fillMaxSize &androidx.compose.ui.Modifier.Companion  size &androidx.compose.ui.Modifier.Companion  clip androidx.compose.ui.draw  Offset androidx.compose.ui.geometry  Size androidx.compose.ui.geometry  plus #androidx.compose.ui.geometry.Offset  x #androidx.compose.ui.geometry.Offset  y #androidx.compose.ui.geometry.Offset  height !androidx.compose.ui.geometry.Size  width !androidx.compose.ui.geometry.Size  ActivityResultContracts androidx.compose.ui.graphics  	Alignment androidx.compose.ui.graphics  Box androidx.compose.ui.graphics  Bundle androidx.compose.ui.graphics  Canvas androidx.compose.ui.graphics  CircleShape androidx.compose.ui.graphics  Color androidx.compose.ui.graphics  ComponentActivity androidx.compose.ui.graphics  
Composable androidx.compose.ui.graphics  ContentScale androidx.compose.ui.graphics  FloatingActionButton androidx.compose.ui.graphics  Icon androidx.compose.ui.graphics  Icons androidx.compose.ui.graphics  Image androidx.compose.ui.graphics  
MagnifierView androidx.compose.ui.graphics  
MainScreen androidx.compose.ui.graphics  Modifier androidx.compose.ui.graphics  Offset androidx.compose.ui.graphics  Preview androidx.compose.ui.graphics  Unit androidx.compose.ui.graphics  Uri androidx.compose.ui.graphics  
Zuijiji2Theme androidx.compose.ui.graphics  align androidx.compose.ui.graphics  androidx androidx.compose.ui.graphics  
background androidx.compose.ui.graphics  clip androidx.compose.ui.graphics  fillMaxSize androidx.compose.ui.graphics  getValue androidx.compose.ui.graphics  let androidx.compose.ui.graphics  mutableStateOf androidx.compose.ui.graphics  offset androidx.compose.ui.graphics  padding androidx.compose.ui.graphics  provideDelegate androidx.compose.ui.graphics  remember androidx.compose.ui.graphics  rememberAsyncImagePainter androidx.compose.ui.graphics  setValue androidx.compose.ui.graphics  size androidx.compose.ui.graphics  with androidx.compose.ui.graphics  Black "androidx.compose.ui.graphics.Color  	Companion "androidx.compose.ui.graphics.Color  Transparent "androidx.compose.ui.graphics.Color  White "androidx.compose.ui.graphics.Color  copy "androidx.compose.ui.graphics.Color  Black ,androidx.compose.ui.graphics.Color.Companion  Transparent ,androidx.compose.ui.graphics.Color.Companion  White ,androidx.compose.ui.graphics.Color.Companion  	DrawScope &androidx.compose.ui.graphics.drawscope  Stroke &androidx.compose.ui.graphics.drawscope  clipPath &androidx.compose.ui.graphics.drawscope  Color 0androidx.compose.ui.graphics.drawscope.DrawScope  Offset 0androidx.compose.ui.graphics.drawscope.DrawScope  androidx 0androidx.compose.ui.graphics.drawscope.DrawScope  dp 0androidx.compose.ui.graphics.drawscope.DrawScope  
drawCircle 0androidx.compose.ui.graphics.drawscope.DrawScope  size 0androidx.compose.ui.graphics.drawscope.DrawScope  toPx 0androidx.compose.ui.graphics.drawscope.DrawScope  ImageVector #androidx.compose.ui.graphics.vector  PointerInputChange !androidx.compose.ui.input.pointer  PointerInputScope !androidx.compose.ui.input.pointer  pointerInput !androidx.compose.ui.input.pointer  detectDragGestures 3androidx.compose.ui.input.pointer.PointerInputScope  ContentScale androidx.compose.ui.layout  	Companion 'androidx.compose.ui.layout.ContentScale  Crop 'androidx.compose.ui.layout.ContentScale  Crop 1androidx.compose.ui.layout.ContentScale.Companion  LocalContext androidx.compose.ui.platform  LocalDensity androidx.compose.ui.platform  	TextStyle androidx.compose.ui.text  
FontFamily androidx.compose.ui.text.font  
FontWeight androidx.compose.ui.text.font  SystemFontFamily androidx.compose.ui.text.font  	Companion (androidx.compose.ui.text.font.FontFamily  Default (androidx.compose.ui.text.font.FontFamily  Default 2androidx.compose.ui.text.font.FontFamily.Companion  	Companion (androidx.compose.ui.text.font.FontWeight  Normal (androidx.compose.ui.text.font.FontWeight  Normal 2androidx.compose.ui.text.font.FontWeight.Companion  Preview #androidx.compose.ui.tooling.preview  Density androidx.compose.ui.unit  Dp androidx.compose.ui.unit  TextUnit androidx.compose.ui.unit  dp androidx.compose.ui.unit  getDp androidx.compose.ui.unit  sp androidx.compose.ui.unit  toDp  androidx.compose.ui.unit.Density  toPx  androidx.compose.ui.unit.Density  times androidx.compose.ui.unit.Dp  toPx androidx.compose.ui.unit.Dp  
unaryMinus androidx.compose.ui.unit.Dp  toDp $androidx.compose.ui.unit.FontScaling  Bundle #androidx.core.app.ComponentActivity  
MainScreen #androidx.core.app.ComponentActivity  
Zuijiji2Theme #androidx.core.app.ComponentActivity  enableEdgeToEdge #androidx.core.app.ComponentActivity  
setContent #androidx.core.app.ComponentActivity  AsyncImagePainter coil.compose  rememberAsyncImagePainter coil.compose  ActivityResultContracts com.example.zuijiji2  	Alignment com.example.zuijiji2  Box com.example.zuijiji2  Bundle com.example.zuijiji2  Canvas com.example.zuijiji2  CircleShape com.example.zuijiji2  Color com.example.zuijiji2  ComponentActivity com.example.zuijiji2  
Composable com.example.zuijiji2  ContentScale com.example.zuijiji2  FloatingActionButton com.example.zuijiji2  Icon com.example.zuijiji2  Icons com.example.zuijiji2  Image com.example.zuijiji2  
MagnifierView com.example.zuijiji2  MainActivity com.example.zuijiji2  
MainScreen com.example.zuijiji2  MainScreenPreview com.example.zuijiji2  Modifier com.example.zuijiji2  Offset com.example.zuijiji2  Preview com.example.zuijiji2  Unit com.example.zuijiji2  Uri com.example.zuijiji2  
Zuijiji2Theme com.example.zuijiji2  align com.example.zuijiji2  androidx com.example.zuijiji2  
background com.example.zuijiji2  clip com.example.zuijiji2  fillMaxSize com.example.zuijiji2  getValue com.example.zuijiji2  let com.example.zuijiji2  mutableStateOf com.example.zuijiji2  offset com.example.zuijiji2  padding com.example.zuijiji2  provideDelegate com.example.zuijiji2  remember com.example.zuijiji2  rememberAsyncImagePainter com.example.zuijiji2  setValue com.example.zuijiji2  size com.example.zuijiji2  with com.example.zuijiji2  
MainScreen !com.example.zuijiji2.MainActivity  
Zuijiji2Theme !com.example.zuijiji2.MainActivity  enableEdgeToEdge !com.example.zuijiji2.MainActivity  
setContent !com.example.zuijiji2.MainActivity  Boolean com.example.zuijiji2.ui.theme  Build com.example.zuijiji2.ui.theme  
Composable com.example.zuijiji2.ui.theme  DarkColorScheme com.example.zuijiji2.ui.theme  
FontFamily com.example.zuijiji2.ui.theme  
FontWeight com.example.zuijiji2.ui.theme  LightColorScheme com.example.zuijiji2.ui.theme  Pink40 com.example.zuijiji2.ui.theme  Pink80 com.example.zuijiji2.ui.theme  Purple40 com.example.zuijiji2.ui.theme  Purple80 com.example.zuijiji2.ui.theme  PurpleGrey40 com.example.zuijiji2.ui.theme  PurpleGrey80 com.example.zuijiji2.ui.theme  
Typography com.example.zuijiji2.ui.theme  Unit com.example.zuijiji2.ui.theme  
Zuijiji2Theme com.example.zuijiji2.ui.theme  	Function0 kotlin  	Function1 kotlin  	Function2 kotlin  let kotlin  with kotlin  not kotlin.Boolean  dp 
kotlin.Double  sp 
kotlin.Double  div kotlin.Float  minus kotlin.Float  times kotlin.Float  toDp kotlin.Float  invoke kotlin.Function1  	compareTo 
kotlin.Int  SuspendFunction1 kotlin.coroutines  sqrt kotlin.math  KMutableProperty0 kotlin.reflect  offset &androidx.compose.ui.Modifier.Companion  consume 4androidx.compose.ui.input.pointer.PointerInputChange  ActivityResultContracts androidx.compose.animation.core  	Alignment androidx.compose.animation.core  Box androidx.compose.animation.core  Brush androidx.compose.animation.core  Bundle androidx.compose.animation.core  Canvas androidx.compose.animation.core  CircleShape androidx.compose.animation.core  Color androidx.compose.animation.core  ComponentActivity androidx.compose.animation.core  
Composable androidx.compose.animation.core  ContentScale androidx.compose.animation.core  Easing androidx.compose.animation.core  FastOutSlowInEasing androidx.compose.animation.core  FloatingActionButton androidx.compose.animation.core  Icon androidx.compose.animation.core  Icons androidx.compose.animation.core  Image androidx.compose.animation.core  InfiniteRepeatableSpec androidx.compose.animation.core  InfiniteTransition androidx.compose.animation.core  LinearEasing androidx.compose.animation.core  LiquidGlassButton androidx.compose.animation.core  LiquidGlassEffect androidx.compose.animation.core  LiquidGlassMagnifierView androidx.compose.animation.core  
MainScreen androidx.compose.animation.core  Modifier androidx.compose.animation.core  Offset androidx.compose.animation.core  PI androidx.compose.animation.core  Preview androidx.compose.animation.core  
RepeatMode androidx.compose.animation.core  String androidx.compose.animation.core  	TweenSpec androidx.compose.animation.core  Unit androidx.compose.animation.core  Uri androidx.compose.animation.core  
Zuijiji2Theme androidx.compose.animation.core  align androidx.compose.animation.core  androidx androidx.compose.animation.core  animateFloat androidx.compose.animation.core  
background androidx.compose.animation.core  blur androidx.compose.animation.core  clip androidx.compose.animation.core  
coerceAtLeast androidx.compose.animation.core  cos androidx.compose.animation.core  fillMaxSize androidx.compose.animation.core  getValue androidx.compose.animation.core  infiniteRepeatable androidx.compose.animation.core  let androidx.compose.animation.core  linearGradient androidx.compose.animation.core  listOf androidx.compose.animation.core  mutableStateOf androidx.compose.animation.core  offset androidx.compose.animation.core  padding androidx.compose.animation.core  provideDelegate androidx.compose.animation.core  radialGradient androidx.compose.animation.core  remember androidx.compose.animation.core  rememberAsyncImagePainter androidx.compose.animation.core  rememberInfiniteTransition androidx.compose.animation.core  setValue androidx.compose.animation.core  sin androidx.compose.animation.core  size androidx.compose.animation.core  tween androidx.compose.animation.core  with androidx.compose.animation.core  animateFloat 2androidx.compose.animation.core.InfiniteTransition  Restart *androidx.compose.animation.core.RepeatMode  Reverse *androidx.compose.animation.core.RepeatMode  compose (androidx.compose.animation.core.androidx  ui 0androidx.compose.animation.core.androidx.compose  graphics 3androidx.compose.animation.core.androidx.compose.ui  vector <androidx.compose.animation.core.androidx.compose.ui.graphics  ImageVector Candroidx.compose.animation.core.androidx.compose.ui.graphics.vector  Brush "androidx.compose.foundation.layout  FastOutSlowInEasing "androidx.compose.foundation.layout  LinearEasing "androidx.compose.foundation.layout  LiquidGlassButton "androidx.compose.foundation.layout  LiquidGlassEffect "androidx.compose.foundation.layout  LiquidGlassMagnifierView "androidx.compose.foundation.layout  PI "androidx.compose.foundation.layout  
RepeatMode "androidx.compose.foundation.layout  String "androidx.compose.foundation.layout  animateFloat "androidx.compose.foundation.layout  blur "androidx.compose.foundation.layout  
coerceAtLeast "androidx.compose.foundation.layout  cos "androidx.compose.foundation.layout  infiniteRepeatable "androidx.compose.foundation.layout  linearGradient "androidx.compose.foundation.layout  listOf "androidx.compose.foundation.layout  radialGradient "androidx.compose.foundation.layout  rememberInfiniteTransition "androidx.compose.foundation.layout  sin "androidx.compose.foundation.layout  tween "androidx.compose.foundation.layout  Brush +androidx.compose.foundation.layout.BoxScope  LiquidGlassButton +androidx.compose.foundation.layout.BoxScope  LiquidGlassEffect +androidx.compose.foundation.layout.BoxScope  LiquidGlassMagnifierView +androidx.compose.foundation.layout.BoxScope  PI +androidx.compose.foundation.layout.BoxScope  blur +androidx.compose.foundation.layout.BoxScope  cos +androidx.compose.foundation.layout.BoxScope  linearGradient +androidx.compose.foundation.layout.BoxScope  listOf +androidx.compose.foundation.layout.BoxScope  radialGradient +androidx.compose.foundation.layout.BoxScope  sin +androidx.compose.foundation.layout.BoxScope  compose +androidx.compose.foundation.layout.androidx  ui 3androidx.compose.foundation.layout.androidx.compose  graphics 6androidx.compose.foundation.layout.androidx.compose.ui  vector ?androidx.compose.foundation.layout.androidx.compose.ui.graphics  ImageVector Fandroidx.compose.foundation.layout.androidx.compose.ui.graphics.vector  Brush androidx.compose.material3  FastOutSlowInEasing androidx.compose.material3  LinearEasing androidx.compose.material3  LiquidGlassButton androidx.compose.material3  LiquidGlassEffect androidx.compose.material3  LiquidGlassMagnifierView androidx.compose.material3  PI androidx.compose.material3  
RepeatMode androidx.compose.material3  String androidx.compose.material3  animateFloat androidx.compose.material3  blur androidx.compose.material3  
coerceAtLeast androidx.compose.material3  cos androidx.compose.material3  infiniteRepeatable androidx.compose.material3  linearGradient androidx.compose.material3  listOf androidx.compose.material3  radialGradient androidx.compose.material3  rememberInfiniteTransition androidx.compose.material3  sin androidx.compose.material3  tween androidx.compose.material3  compose #androidx.compose.material3.androidx  ui +androidx.compose.material3.androidx.compose  graphics .androidx.compose.material3.androidx.compose.ui  vector 7androidx.compose.material3.androidx.compose.ui.graphics  ImageVector >androidx.compose.material3.androidx.compose.ui.graphics.vector  Brush androidx.compose.runtime  FastOutSlowInEasing androidx.compose.runtime  LinearEasing androidx.compose.runtime  LiquidGlassButton androidx.compose.runtime  LiquidGlassEffect androidx.compose.runtime  LiquidGlassMagnifierView androidx.compose.runtime  PI androidx.compose.runtime  
RepeatMode androidx.compose.runtime  State androidx.compose.runtime  String androidx.compose.runtime  animateFloat androidx.compose.runtime  blur androidx.compose.runtime  
coerceAtLeast androidx.compose.runtime  cos androidx.compose.runtime  infiniteRepeatable androidx.compose.runtime  linearGradient androidx.compose.runtime  listOf androidx.compose.runtime  radialGradient androidx.compose.runtime  rememberInfiniteTransition androidx.compose.runtime  sin androidx.compose.runtime  tween androidx.compose.runtime  getValue androidx.compose.runtime.State  provideDelegate androidx.compose.runtime.State  compose !androidx.compose.runtime.androidx  ui )androidx.compose.runtime.androidx.compose  graphics ,androidx.compose.runtime.androidx.compose.ui  vector 5androidx.compose.runtime.androidx.compose.ui.graphics  ImageVector <androidx.compose.runtime.androidx.compose.ui.graphics.vector  blur androidx.compose.ui.Modifier  drawWithContent androidx.compose.ui.Modifier  blur androidx.compose.ui.draw  drawWithContent androidx.compose.ui.draw  Brush androidx.compose.ui.graphics  FastOutSlowInEasing androidx.compose.ui.graphics  LinearEasing androidx.compose.ui.graphics  LiquidGlassButton androidx.compose.ui.graphics  LiquidGlassEffect androidx.compose.ui.graphics  LiquidGlassMagnifierView androidx.compose.ui.graphics  PI androidx.compose.ui.graphics  
RepeatMode androidx.compose.ui.graphics  String androidx.compose.ui.graphics  animateFloat androidx.compose.ui.graphics  blur androidx.compose.ui.graphics  
coerceAtLeast androidx.compose.ui.graphics  cos androidx.compose.ui.graphics  infiniteRepeatable androidx.compose.ui.graphics  linearGradient androidx.compose.ui.graphics  listOf androidx.compose.ui.graphics  radialGradient androidx.compose.ui.graphics  rememberInfiniteTransition androidx.compose.ui.graphics  sin androidx.compose.ui.graphics  tween androidx.compose.ui.graphics  	Companion "androidx.compose.ui.graphics.Brush  linearGradient "androidx.compose.ui.graphics.Brush  radialGradient "androidx.compose.ui.graphics.Brush  linearGradient ,androidx.compose.ui.graphics.Brush.Companion  radialGradient ,androidx.compose.ui.graphics.Brush.Companion  Blue "androidx.compose.ui.graphics.Color  Cyan "androidx.compose.ui.graphics.Color  Blue ,androidx.compose.ui.graphics.Color.Companion  Cyan ,androidx.compose.ui.graphics.Color.Companion  compose %androidx.compose.ui.graphics.androidx  ui -androidx.compose.ui.graphics.androidx.compose  graphics 0androidx.compose.ui.graphics.androidx.compose.ui  vector 9androidx.compose.ui.graphics.androidx.compose.ui.graphics  ImageVector @androidx.compose.ui.graphics.androidx.compose.ui.graphics.vector  ContentDrawScope &androidx.compose.ui.graphics.drawscope  Brush 7androidx.compose.ui.graphics.drawscope.ContentDrawScope  Color 7androidx.compose.ui.graphics.drawscope.ContentDrawScope  center 7androidx.compose.ui.graphics.drawscope.ContentDrawScope  
drawCircle 7androidx.compose.ui.graphics.drawscope.ContentDrawScope  drawContent 7androidx.compose.ui.graphics.drawscope.ContentDrawScope  listOf 7androidx.compose.ui.graphics.drawscope.ContentDrawScope  radialGradient 7androidx.compose.ui.graphics.drawscope.ContentDrawScope  size 7androidx.compose.ui.graphics.drawscope.ContentDrawScope  Brush 0androidx.compose.ui.graphics.drawscope.DrawScope  PI 0androidx.compose.ui.graphics.drawscope.DrawScope  center 0androidx.compose.ui.graphics.drawscope.DrawScope  
coerceAtLeast 0androidx.compose.ui.graphics.drawscope.DrawScope  cos 0androidx.compose.ui.graphics.drawscope.DrawScope  drawRect 0androidx.compose.ui.graphics.drawscope.DrawScope  linearGradient 0androidx.compose.ui.graphics.drawscope.DrawScope  listOf 0androidx.compose.ui.graphics.drawscope.DrawScope  radialGradient 0androidx.compose.ui.graphics.drawscope.DrawScope  sin 0androidx.compose.ui.graphics.drawscope.DrawScope  Brush com.example.zuijiji2  FastOutSlowInEasing com.example.zuijiji2  LinearEasing com.example.zuijiji2  LiquidGlassButton com.example.zuijiji2  LiquidGlassEffect com.example.zuijiji2  LiquidGlassMagnifierView com.example.zuijiji2  PI com.example.zuijiji2  
RepeatMode com.example.zuijiji2  String com.example.zuijiji2  animateFloat com.example.zuijiji2  blur com.example.zuijiji2  
coerceAtLeast com.example.zuijiji2  cos com.example.zuijiji2  infiniteRepeatable com.example.zuijiji2  linearGradient com.example.zuijiji2  listOf com.example.zuijiji2  radialGradient com.example.zuijiji2  rememberInfiniteTransition com.example.zuijiji2  sin com.example.zuijiji2  tween com.example.zuijiji2  compose com.example.zuijiji2.androidx  ui %com.example.zuijiji2.androidx.compose  graphics (com.example.zuijiji2.androidx.compose.ui  vector 1com.example.zuijiji2.androidx.compose.ui.graphics  ImageVector 8com.example.zuijiji2.androidx.compose.ui.graphics.vector  div 
kotlin.Double  times 
kotlin.Double  toFloat 
kotlin.Double  
coerceAtLeast kotlin.Float  plus kotlin.Float  
unaryMinus kotlin.Float  rangeTo 
kotlin.Int  times 
kotlin.Int  IntIterator kotlin.collections  List kotlin.collections  listOf kotlin.collections  hasNext kotlin.collections.IntIterator  next kotlin.collections.IntIterator  PI kotlin.math  cos kotlin.math  sin kotlin.math  IntRange 
kotlin.ranges  	LongRange 
kotlin.ranges  
coerceAtLeast 
kotlin.ranges  iterator kotlin.ranges.IntProgression  iterator kotlin.ranges.IntRange  
KProperty0 kotlin.reflect  Greeting android.app.Activity  Modifier android.app.Activity  Scaffold android.app.Activity  fillMaxSize android.app.Activity  padding android.app.Activity  Greeting android.content.Context  Modifier android.content.Context  Scaffold android.content.Context  fillMaxSize android.content.Context  padding android.content.Context  Greeting android.content.ContextWrapper  Modifier android.content.ContextWrapper  Scaffold android.content.ContextWrapper  fillMaxSize android.content.ContextWrapper  padding android.content.ContextWrapper  Greeting  android.view.ContextThemeWrapper  Modifier  android.view.ContextThemeWrapper  Scaffold  android.view.ContextThemeWrapper  fillMaxSize  android.view.ContextThemeWrapper  padding  android.view.ContextThemeWrapper  Greeting #androidx.activity.ComponentActivity  Modifier #androidx.activity.ComponentActivity  Scaffold #androidx.activity.ComponentActivity  fillMaxSize #androidx.activity.ComponentActivity  padding #androidx.activity.ComponentActivity  Greeting -androidx.activity.ComponentActivity.Companion  Modifier -androidx.activity.ComponentActivity.Companion  Scaffold -androidx.activity.ComponentActivity.Companion  fillMaxSize -androidx.activity.ComponentActivity.Companion  padding -androidx.activity.ComponentActivity.Companion  
PaddingValues "androidx.compose.foundation.layout  Scaffold androidx.compose.material3  Text androidx.compose.material3  padding &androidx.compose.ui.Modifier.Companion  Greeting #androidx.core.app.ComponentActivity  Modifier #androidx.core.app.ComponentActivity  Scaffold #androidx.core.app.ComponentActivity  fillMaxSize #androidx.core.app.ComponentActivity  padding #androidx.core.app.ComponentActivity  Greeting com.example.zuijiji2  GreetingPreview com.example.zuijiji2  Scaffold com.example.zuijiji2  Greeting !com.example.zuijiji2.MainActivity  Modifier !com.example.zuijiji2.MainActivity  Scaffold !com.example.zuijiji2.MainActivity  fillMaxSize !com.example.zuijiji2.MainActivity  padding !com.example.zuijiji2.MainActivity  BorderStroke androidx.compose.foundation  border androidx.compose.foundation  Arrangement "androidx.compose.foundation.layout  Boolean "androidx.compose.foundation.layout  Button "androidx.compose.foundation.layout  ButtonDefaults "androidx.compose.foundation.layout  Column "androidx.compose.foundation.layout  ColumnScope "androidx.compose.foundation.layout  EnhancedGlassDialog "androidx.compose.foundation.layout  
FontWeight "androidx.compose.foundation.layout  GlassButton "androidx.compose.foundation.layout  ImageRequest "androidx.compose.foundation.layout  LocalContext "androidx.compose.foundation.layout  RoundedCornerShape "androidx.compose.foundation.layout  Row "androidx.compose.foundation.layout  RowScope "androidx.compose.foundation.layout  Spacer "androidx.compose.foundation.layout  Text "androidx.compose.foundation.layout  border "androidx.compose.foundation.layout  buttonColors "androidx.compose.foundation.layout  fillMaxWidth "androidx.compose.foundation.layout  height "androidx.compose.foundation.layout  run "androidx.compose.foundation.layout  spacedBy "androidx.compose.foundation.layout  verticalGradient "androidx.compose.foundation.layout  weight "androidx.compose.foundation.layout  width "androidx.compose.foundation.layout  Center .androidx.compose.foundation.layout.Arrangement  
Horizontal .androidx.compose.foundation.layout.Arrangement  HorizontalOrVertical .androidx.compose.foundation.layout.Arrangement  SpaceEvenly .androidx.compose.foundation.layout.Arrangement  Vertical .androidx.compose.foundation.layout.Arrangement  spacedBy .androidx.compose.foundation.layout.Arrangement  Arrangement +androidx.compose.foundation.layout.BoxScope  Button +androidx.compose.foundation.layout.BoxScope  ButtonDefaults +androidx.compose.foundation.layout.BoxScope  Column +androidx.compose.foundation.layout.BoxScope  EnhancedGlassDialog +androidx.compose.foundation.layout.BoxScope  
FontWeight +androidx.compose.foundation.layout.BoxScope  GlassButton +androidx.compose.foundation.layout.BoxScope  ImageRequest +androidx.compose.foundation.layout.BoxScope  LocalContext +androidx.compose.foundation.layout.BoxScope  RoundedCornerShape +androidx.compose.foundation.layout.BoxScope  Row +androidx.compose.foundation.layout.BoxScope  Spacer +androidx.compose.foundation.layout.BoxScope  Text +androidx.compose.foundation.layout.BoxScope  border +androidx.compose.foundation.layout.BoxScope  buttonColors +androidx.compose.foundation.layout.BoxScope  fillMaxWidth +androidx.compose.foundation.layout.BoxScope  height +androidx.compose.foundation.layout.BoxScope  run +androidx.compose.foundation.layout.BoxScope  sp +androidx.compose.foundation.layout.BoxScope  spacedBy +androidx.compose.foundation.layout.BoxScope  verticalGradient +androidx.compose.foundation.layout.BoxScope  weight +androidx.compose.foundation.layout.BoxScope  width +androidx.compose.foundation.layout.BoxScope  Arrangement .androidx.compose.foundation.layout.ColumnScope  Color .androidx.compose.foundation.layout.ColumnScope  
FontWeight .androidx.compose.foundation.layout.ColumnScope  GlassButton .androidx.compose.foundation.layout.ColumnScope  Modifier .androidx.compose.foundation.layout.ColumnScope  Row .androidx.compose.foundation.layout.ColumnScope  Spacer .androidx.compose.foundation.layout.ColumnScope  Text .androidx.compose.foundation.layout.ColumnScope  dp .androidx.compose.foundation.layout.ColumnScope  height .androidx.compose.foundation.layout.ColumnScope  sp .androidx.compose.foundation.layout.ColumnScope  spacedBy .androidx.compose.foundation.layout.ColumnScope  weight .androidx.compose.foundation.layout.ColumnScope  Brush +androidx.compose.foundation.layout.RowScope  Button +androidx.compose.foundation.layout.RowScope  ButtonDefaults +androidx.compose.foundation.layout.RowScope  Color +androidx.compose.foundation.layout.RowScope  
FontWeight +androidx.compose.foundation.layout.RowScope  GlassButton +androidx.compose.foundation.layout.RowScope  Modifier +androidx.compose.foundation.layout.RowScope  RoundedCornerShape +androidx.compose.foundation.layout.RowScope  Spacer +androidx.compose.foundation.layout.RowScope  Text +androidx.compose.foundation.layout.RowScope  androidx +androidx.compose.foundation.layout.RowScope  
background +androidx.compose.foundation.layout.RowScope  buttonColors +androidx.compose.foundation.layout.RowScope  clip +androidx.compose.foundation.layout.RowScope  dp +androidx.compose.foundation.layout.RowScope  height +androidx.compose.foundation.layout.RowScope  linearGradient +androidx.compose.foundation.layout.RowScope  listOf +androidx.compose.foundation.layout.RowScope  sp +androidx.compose.foundation.layout.RowScope  weight +androidx.compose.foundation.layout.RowScope  width +androidx.compose.foundation.layout.RowScope  Arrangement androidx.compose.material3  Boolean androidx.compose.material3  Button androidx.compose.material3  ButtonColors androidx.compose.material3  ButtonDefaults androidx.compose.material3  Column androidx.compose.material3  EnhancedGlassDialog androidx.compose.material3  
FontWeight androidx.compose.material3  GlassButton androidx.compose.material3  ImageRequest androidx.compose.material3  LocalContext androidx.compose.material3  RoundedCornerShape androidx.compose.material3  Row androidx.compose.material3  Spacer androidx.compose.material3  border androidx.compose.material3  buttonColors androidx.compose.material3  fillMaxWidth androidx.compose.material3  height androidx.compose.material3  run androidx.compose.material3  spacedBy androidx.compose.material3  verticalGradient androidx.compose.material3  weight androidx.compose.material3  width androidx.compose.material3  buttonColors )androidx.compose.material3.ButtonDefaults  Arrangement androidx.compose.runtime  Boolean androidx.compose.runtime  Button androidx.compose.runtime  ButtonDefaults androidx.compose.runtime  Column androidx.compose.runtime  EnhancedGlassDialog androidx.compose.runtime  
FontWeight androidx.compose.runtime  GlassButton androidx.compose.runtime  ImageRequest androidx.compose.runtime  LocalContext androidx.compose.runtime  RoundedCornerShape androidx.compose.runtime  Row androidx.compose.runtime  Spacer androidx.compose.runtime  Text androidx.compose.runtime  border androidx.compose.runtime  buttonColors androidx.compose.runtime  fillMaxWidth androidx.compose.runtime  height androidx.compose.runtime  run androidx.compose.runtime  spacedBy androidx.compose.runtime  verticalGradient androidx.compose.runtime  weight androidx.compose.runtime  width androidx.compose.runtime  BottomCenter androidx.compose.ui.Alignment  Center androidx.compose.ui.Alignment  CenterHorizontally androidx.compose.ui.Alignment  
Horizontal androidx.compose.ui.Alignment  BottomCenter 'androidx.compose.ui.Alignment.Companion  Center 'androidx.compose.ui.Alignment.Companion  CenterHorizontally 'androidx.compose.ui.Alignment.Companion  border androidx.compose.ui.Modifier  fillMaxWidth androidx.compose.ui.Modifier  height androidx.compose.ui.Modifier  weight androidx.compose.ui.Modifier  width androidx.compose.ui.Modifier  height &androidx.compose.ui.Modifier.Companion  weight &androidx.compose.ui.Modifier.Companion  width &androidx.compose.ui.Modifier.Companion  verticalGradient "androidx.compose.ui.graphics.Brush  verticalGradient ,androidx.compose.ui.graphics.Brush.Companion  Bold (androidx.compose.ui.text.font.FontWeight  Medium (androidx.compose.ui.text.font.FontWeight  SemiBold (androidx.compose.ui.text.font.FontWeight  Bold 2androidx.compose.ui.text.font.FontWeight.Companion  Medium 2androidx.compose.ui.text.font.FontWeight.Companion  SemiBold 2androidx.compose.ui.text.font.FontWeight.Companion  Dialog androidx.compose.ui.window  DialogProperties androidx.compose.ui.window  ImageRequest coil.request  Builder coil.request.ImageRequest  build !coil.request.ImageRequest.Builder  data !coil.request.ImageRequest.Builder  Arrangement com.example.zuijiji2  Boolean com.example.zuijiji2  Button com.example.zuijiji2  ButtonDefaults com.example.zuijiji2  Column com.example.zuijiji2  EnhancedGlassDialog com.example.zuijiji2  
FontWeight com.example.zuijiji2  GlassButton com.example.zuijiji2  ImageRequest com.example.zuijiji2  LocalContext com.example.zuijiji2  RoundedCornerShape com.example.zuijiji2  Row com.example.zuijiji2  Spacer com.example.zuijiji2  Text com.example.zuijiji2  border com.example.zuijiji2  buttonColors com.example.zuijiji2  fillMaxWidth com.example.zuijiji2  height com.example.zuijiji2  run com.example.zuijiji2  spacedBy com.example.zuijiji2  verticalGradient com.example.zuijiji2  weight com.example.zuijiji2  width com.example.zuijiji2  run kotlin  
AsyncImage "androidx.compose.foundation.layout  	BlendMode "androidx.compose.foundation.layout  DraggableMagnifier "androidx.compose.foundation.layout  
MaterialTheme "androidx.compose.foundation.layout  R "androidx.compose.foundation.layout  coerceIn "androidx.compose.foundation.layout  painterResource "androidx.compose.foundation.layout  
AsyncImage +androidx.compose.foundation.layout.BoxScope  	BlendMode +androidx.compose.foundation.layout.BoxScope  DraggableMagnifier +androidx.compose.foundation.layout.BoxScope  
MaterialTheme +androidx.compose.foundation.layout.BoxScope  R +androidx.compose.foundation.layout.BoxScope  painterResource +androidx.compose.foundation.layout.BoxScope  
AsyncImage androidx.compose.material3  	BlendMode androidx.compose.material3  DraggableMagnifier androidx.compose.material3  R androidx.compose.material3  coerceIn androidx.compose.material3  painterResource androidx.compose.material3  	onPrimary &androidx.compose.material3.ColorScheme  onSecondary &androidx.compose.material3.ColorScheme  primary &androidx.compose.material3.ColorScheme  	secondary &androidx.compose.material3.ColorScheme  colorScheme (androidx.compose.material3.MaterialTheme  
AsyncImage androidx.compose.runtime  	BlendMode androidx.compose.runtime  DraggableMagnifier androidx.compose.runtime  
MaterialTheme androidx.compose.runtime  R androidx.compose.runtime  coerceIn androidx.compose.runtime  painterResource androidx.compose.runtime  minDimension !androidx.compose.ui.geometry.Size  	BlendMode androidx.compose.ui.graphics  Clear &androidx.compose.ui.graphics.BlendMode  	Companion &androidx.compose.ui.graphics.BlendMode  Clear 0androidx.compose.ui.graphics.BlendMode.Companion  	BlendMode 0androidx.compose.ui.graphics.drawscope.DrawScope  Painter $androidx.compose.ui.graphics.painter  Offset 3androidx.compose.ui.input.pointer.PointerInputScope  coerceIn 3androidx.compose.ui.input.pointer.PointerInputScope  size 3androidx.compose.ui.input.pointer.PointerInputScope  painterResource androidx.compose.ui.res  IntSize androidx.compose.ui.unit  height  androidx.compose.ui.unit.IntSize  width  androidx.compose.ui.unit.IntSize  
AsyncImage coil.compose  
AsyncImage com.example.zuijiji2  	BlendMode com.example.zuijiji2  DraggableMagnifier com.example.zuijiji2  
MaterialTheme com.example.zuijiji2  R com.example.zuijiji2  coerceIn com.example.zuijiji2  painterResource com.example.zuijiji2  ic_image_import com.example.zuijiji2.R.drawable  ic_magnifier com.example.zuijiji2.R.drawable  coerceIn kotlin.Float  minus 
kotlin.Int  coerceIn 
kotlin.ranges  ImageBitmap "androidx.compose.foundation.layout  IntSize "androidx.compose.foundation.layout  MagnifiableImage "androidx.compose.foundation.layout  Rect "androidx.compose.foundation.layout  Size "androidx.compose.foundation.layout  
intrinsicSize "androidx.compose.foundation.layout  	toIntSize "androidx.compose.foundation.layout  	translate "androidx.compose.foundation.layout  MagnifiableImage +androidx.compose.foundation.layout.BoxScope  ImageBitmap androidx.compose.material3  IntSize androidx.compose.material3  MagnifiableImage androidx.compose.material3  Rect androidx.compose.material3  Size androidx.compose.material3  
intrinsicSize androidx.compose.material3  	toIntSize androidx.compose.material3  	translate androidx.compose.material3  ImageBitmap androidx.compose.runtime  IntSize androidx.compose.runtime  MagnifiableImage androidx.compose.runtime  Rect androidx.compose.runtime  Size androidx.compose.runtime  
intrinsicSize androidx.compose.runtime  	toIntSize androidx.compose.runtime  	translate androidx.compose.runtime  onGloballyPositioned androidx.compose.ui.Modifier  Rect androidx.compose.ui.geometry  let #androidx.compose.ui.geometry.Offset  left !androidx.compose.ui.geometry.Rect  top !androidx.compose.ui.geometry.Rect  let !androidx.compose.ui.geometry.Size  	toIntSize !androidx.compose.ui.geometry.Size  ImageBitmap androidx.compose.ui.graphics  IntSize androidx.compose.ui.graphics  MagnifiableImage androidx.compose.ui.graphics  
MaterialTheme androidx.compose.ui.graphics  R androidx.compose.ui.graphics  Rect androidx.compose.ui.graphics  Size androidx.compose.ui.graphics  coerceIn androidx.compose.ui.graphics  
intrinsicSize androidx.compose.ui.graphics  painterResource androidx.compose.ui.graphics  	toIntSize androidx.compose.ui.graphics  	translate androidx.compose.ui.graphics  Red "androidx.compose.ui.graphics.Color  Red ,androidx.compose.ui.graphics.Color.Companion  clipRect &androidx.compose.ui.graphics.drawscope  scale &androidx.compose.ui.graphics.drawscope  	translate &androidx.compose.ui.graphics.drawscope  ImageBitmap 0androidx.compose.ui.graphics.drawscope.DrawScope  Rect 0androidx.compose.ui.graphics.drawscope.DrawScope  Size 0androidx.compose.ui.graphics.drawscope.DrawScope  clipRect 0androidx.compose.ui.graphics.drawscope.DrawScope  draw 0androidx.compose.ui.graphics.drawscope.DrawScope  	drawImage 0androidx.compose.ui.graphics.drawscope.DrawScope  drawLine 0androidx.compose.ui.graphics.drawscope.DrawScope  
intrinsicSize 0androidx.compose.ui.graphics.drawscope.DrawScope  invoke 0androidx.compose.ui.graphics.drawscope.DrawScope  let 0androidx.compose.ui.graphics.drawscope.DrawScope  scale 0androidx.compose.ui.graphics.drawscope.DrawScope  	toIntSize 0androidx.compose.ui.graphics.drawscope.DrawScope  	translate 0androidx.compose.ui.graphics.drawscope.DrawScope  with 0androidx.compose.ui.graphics.drawscope.DrawScope  draw ,androidx.compose.ui.graphics.painter.Painter  let 3androidx.compose.ui.input.pointer.PointerInputScope  LayoutCoordinates androidx.compose.ui.layout  onGloballyPositioned androidx.compose.ui.layout  size ,androidx.compose.ui.layout.LayoutCoordinates  	toIntSize androidx.compose.ui.unit  toSize androidx.compose.ui.unit  dp  androidx.compose.ui.unit.Density  	Companion  androidx.compose.ui.unit.IntSize  Zero  androidx.compose.ui.unit.IntSize  Zero *androidx.compose.ui.unit.IntSize.Companion  draw coil.compose.AsyncImagePainter  
intrinsicSize coil.compose.AsyncImagePainter  scale coil.compose.AsyncImagePainter  size coil.compose.AsyncImagePainter  	translate coil.compose.AsyncImagePainter  ImageBitmap com.example.zuijiji2  IntSize com.example.zuijiji2  MagnifiableImage com.example.zuijiji2  Rect com.example.zuijiji2  Size com.example.zuijiji2  
intrinsicSize com.example.zuijiji2  	toIntSize com.example.zuijiji2  	translate com.example.zuijiji2  Nothing kotlin  	compareTo kotlin.Float  invoke kotlin.Float  toInt kotlin.Float  
MaterialTheme .androidx.compose.foundation.layout.ColumnScope  
MaterialTheme +androidx.compose.foundation.layout.RowScope  
typography (androidx.compose.material3.MaterialTheme  	bodyLarge %androidx.compose.material3.Typography  headlineMedium %androidx.compose.material3.Typography  	LightGray "androidx.compose.ui.graphics.Color  	LightGray ,androidx.compose.ui.graphics.Color.Companion  LocalDensity "androidx.compose.foundation.layout  MagnifierWindow "androidx.compose.foundation.layout  android "androidx.compose.foundation.layout  
graphicsLayer "androidx.compose.foundation.layout  max "androidx.compose.foundation.layout  min "androidx.compose.foundation.layout  LocalDensity +androidx.compose.foundation.layout.BoxScope  MagnifierWindow +androidx.compose.foundation.layout.BoxScope  
graphicsLayer +androidx.compose.foundation.layout.BoxScope  max +androidx.compose.foundation.layout.BoxScope  min +androidx.compose.foundation.layout.BoxScope  content *androidx.compose.foundation.layout.android  Context 2androidx.compose.foundation.layout.android.content  LocalDensity androidx.compose.material3  MagnifierWindow androidx.compose.material3  android androidx.compose.material3  
graphicsLayer androidx.compose.material3  max androidx.compose.material3  min androidx.compose.material3  content "androidx.compose.material3.android  Context *androidx.compose.material3.android.content  LocalDensity androidx.compose.runtime  MagnifierWindow androidx.compose.runtime  android androidx.compose.runtime  
graphicsLayer androidx.compose.runtime  max androidx.compose.runtime  min androidx.compose.runtime  content  androidx.compose.runtime.android  Context (androidx.compose.runtime.android.content  
graphicsLayer androidx.compose.ui.Modifier  
graphicsLayer androidx.compose.ui.graphics  LocalDensity com.example.zuijiji2  MagnifierWindow com.example.zuijiji2  android com.example.zuijiji2  
graphicsLayer com.example.zuijiji2  max com.example.zuijiji2  min com.example.zuijiji2  content com.example.zuijiji2.android  Context $com.example.zuijiji2.android.content  max kotlin.collections  min kotlin.collections  max kotlin.math  min kotlin.math  max kotlin.sequences  min kotlin.sequences  max kotlin.text  min kotlin.text  toDp 
kotlin.Int  getValue +androidx.compose.foundation.layout.BoxScope  mutableStateOf +androidx.compose.foundation.layout.BoxScope  provideDelegate +androidx.compose.foundation.layout.BoxScope  remember +androidx.compose.foundation.layout.BoxScope  setValue +androidx.compose.foundation.layout.BoxScope  painter ?androidx.compose.foundation.layout.androidx.compose.ui.graphics  Painter Gandroidx.compose.foundation.layout.androidx.compose.ui.graphics.painter  painter 7androidx.compose.material3.androidx.compose.ui.graphics  Painter ?androidx.compose.material3.androidx.compose.ui.graphics.painter  painter 5androidx.compose.runtime.androidx.compose.ui.graphics  Painter =androidx.compose.runtime.androidx.compose.ui.graphics.painter  TransformOrigin androidx.compose.ui.graphics  painter 1com.example.zuijiji2.androidx.compose.ui.graphics  Painter 9com.example.zuijiji2.androidx.compose.ui.graphics.painter  Fit 'androidx.compose.ui.layout.ContentScale  Fit 1androidx.compose.ui.layout.ContentScale.Companion  pointerInput "androidx.compose.foundation.layout  SpaceBetween .androidx.compose.foundation.layout.Arrangement  Unit +androidx.compose.foundation.layout.BoxScope  coerceIn +androidx.compose.foundation.layout.BoxScope  detectDragGestures +androidx.compose.foundation.layout.BoxScope  pointerInput +androidx.compose.foundation.layout.BoxScope  pointerInput androidx.compose.material3  
bodyMedium %androidx.compose.material3.Typography  pointerInput androidx.compose.runtime  dp 3androidx.compose.ui.input.pointer.PointerInputScope  toPx 3androidx.compose.ui.input.pointer.PointerInputScope  pointerInput com.example.zuijiji2  dp kotlin.Float  toFloat 
kotlin.Int  BoxWithConstraints "androidx.compose.foundation.layout  BoxWithConstraintsScope "androidx.compose.foundation.layout  	Alignment :androidx.compose.foundation.layout.BoxWithConstraintsScope  Arrangement :androidx.compose.foundation.layout.BoxWithConstraintsScope  Box :androidx.compose.foundation.layout.BoxWithConstraintsScope  Button :androidx.compose.foundation.layout.BoxWithConstraintsScope  ButtonDefaults :androidx.compose.foundation.layout.BoxWithConstraintsScope  CircleShape :androidx.compose.foundation.layout.BoxWithConstraintsScope  Color :androidx.compose.foundation.layout.BoxWithConstraintsScope  ContentScale :androidx.compose.foundation.layout.BoxWithConstraintsScope  Image :androidx.compose.foundation.layout.BoxWithConstraintsScope  ImageRequest :androidx.compose.foundation.layout.BoxWithConstraintsScope  
MaterialTheme :androidx.compose.foundation.layout.BoxWithConstraintsScope  Modifier :androidx.compose.foundation.layout.BoxWithConstraintsScope  Offset :androidx.compose.foundation.layout.BoxWithConstraintsScope  Row :androidx.compose.foundation.layout.BoxWithConstraintsScope  Spacer :androidx.compose.foundation.layout.BoxWithConstraintsScope  Text :androidx.compose.foundation.layout.BoxWithConstraintsScope  Unit :androidx.compose.foundation.layout.BoxWithConstraintsScope  align :androidx.compose.foundation.layout.BoxWithConstraintsScope  
background :androidx.compose.foundation.layout.BoxWithConstraintsScope  border :androidx.compose.foundation.layout.BoxWithConstraintsScope  buttonColors :androidx.compose.foundation.layout.BoxWithConstraintsScope  clip :androidx.compose.foundation.layout.BoxWithConstraintsScope  coerceIn :androidx.compose.foundation.layout.BoxWithConstraintsScope  detectDragGestures :androidx.compose.foundation.layout.BoxWithConstraintsScope  dp :androidx.compose.foundation.layout.BoxWithConstraintsScope  fillMaxSize :androidx.compose.foundation.layout.BoxWithConstraintsScope  fillMaxWidth :androidx.compose.foundation.layout.BoxWithConstraintsScope  let :androidx.compose.foundation.layout.BoxWithConstraintsScope  	maxHeight :androidx.compose.foundation.layout.BoxWithConstraintsScope  maxWidth :androidx.compose.foundation.layout.BoxWithConstraintsScope  offset :androidx.compose.foundation.layout.BoxWithConstraintsScope  padding :androidx.compose.foundation.layout.BoxWithConstraintsScope  pointerInput :androidx.compose.foundation.layout.BoxWithConstraintsScope  rememberAsyncImagePainter :androidx.compose.foundation.layout.BoxWithConstraintsScope  run :androidx.compose.foundation.layout.BoxWithConstraintsScope  size :androidx.compose.foundation.layout.BoxWithConstraintsScope  weight :androidx.compose.foundation.layout.BoxWithConstraintsScope  width :androidx.compose.foundation.layout.BoxWithConstraintsScope  BoxWithConstraints androidx.compose.material3  BoxWithConstraints androidx.compose.runtime  value androidx.compose.ui.unit.Dp  BoxWithConstraints com.example.zuijiji2  	IntOffset "androidx.compose.foundation.layout  	IntOffset :androidx.compose.foundation.layout.BoxWithConstraintsScope  with :androidx.compose.foundation.layout.BoxWithConstraintsScope  	IntOffset androidx.compose.material3  	IntOffset androidx.compose.runtime  with 3androidx.compose.ui.input.pointer.PointerInputScope  	IntOffset androidx.compose.ui.unit  	IntOffset  androidx.compose.ui.unit.Density  	IntOffset com.example.zuijiji2  Float "androidx.compose.foundation.layout  MagnifyingGlass "androidx.compose.foundation.layout  Path "androidx.compose.foundation.layout  Stroke "androidx.compose.foundation.layout  apply "androidx.compose.foundation.layout  MagnifyingGlass :androidx.compose.foundation.layout.BoxWithConstraintsScope  androidx :androidx.compose.foundation.layout.BoxWithConstraintsScope  geometry 6androidx.compose.foundation.layout.androidx.compose.ui  Size ?androidx.compose.foundation.layout.androidx.compose.ui.geometry  Float androidx.compose.material3  MagnifyingGlass androidx.compose.material3  Path androidx.compose.material3  Stroke androidx.compose.material3  apply androidx.compose.material3  geometry .androidx.compose.material3.androidx.compose.ui  Size 7androidx.compose.material3.androidx.compose.ui.geometry  Float androidx.compose.runtime  MagnifyingGlass androidx.compose.runtime  Path androidx.compose.runtime  Stroke androidx.compose.runtime  apply androidx.compose.runtime  geometry ,androidx.compose.runtime.androidx.compose.ui  Size 5androidx.compose.runtime.androidx.compose.ui.geometry  minus #androidx.compose.ui.geometry.Offset  Arrangement androidx.compose.ui.graphics  BoxWithConstraints androidx.compose.ui.graphics  Button androidx.compose.ui.graphics  ButtonDefaults androidx.compose.ui.graphics  Float androidx.compose.ui.graphics  ImageRequest androidx.compose.ui.graphics  	IntOffset androidx.compose.ui.graphics  MagnifyingGlass androidx.compose.ui.graphics  Path androidx.compose.ui.graphics  Row androidx.compose.ui.graphics  Spacer androidx.compose.ui.graphics  Stroke androidx.compose.ui.graphics  Text androidx.compose.ui.graphics  apply androidx.compose.ui.graphics  buttonColors androidx.compose.ui.graphics  fillMaxWidth androidx.compose.ui.graphics  pointerInput androidx.compose.ui.graphics  run androidx.compose.ui.graphics  weight androidx.compose.ui.graphics  width androidx.compose.ui.graphics  Offset !androidx.compose.ui.graphics.Path  Rect !androidx.compose.ui.graphics.Path  addOval !androidx.compose.ui.graphics.Path  apply !androidx.compose.ui.graphics.Path  invoke !androidx.compose.ui.graphics.Path  geometry 0androidx.compose.ui.graphics.androidx.compose.ui  Size 9androidx.compose.ui.graphics.androidx.compose.ui.geometry  Path 0androidx.compose.ui.graphics.drawscope.DrawScope  Stroke 0androidx.compose.ui.graphics.drawscope.DrawScope  apply 0androidx.compose.ui.graphics.drawscope.DrawScope  clipPath 0androidx.compose.ui.graphics.drawscope.DrawScope  run 0androidx.compose.ui.graphics.drawscope.DrawScope  Float com.example.zuijiji2  MagnifyingGlass com.example.zuijiji2  Path com.example.zuijiji2  Stroke com.example.zuijiji2  apply com.example.zuijiji2  geometry (com.example.zuijiji2.androidx.compose.ui  Size 1com.example.zuijiji2.androidx.compose.ui.geometry  apply kotlin  Canvas :androidx.compose.foundation.layout.BoxWithConstraintsScope  border androidx.compose.ui.graphics  Gray "androidx.compose.ui.graphics.Color  Gray ,androidx.compose.ui.graphics.Color.Companion  density  androidx.compose.ui.unit.Density  plus 
kotlin.Int  rem 
kotlin.Int  Path +androidx.compose.foundation.layout.BoxScope  Rect +androidx.compose.foundation.layout.BoxScope  apply +androidx.compose.foundation.layout.BoxScope  clipPath +androidx.compose.foundation.layout.BoxScope  scale +androidx.compose.foundation.layout.BoxScope  	translate +androidx.compose.foundation.layout.BoxScope  Brush :androidx.compose.foundation.layout.BoxWithConstraintsScope  Path :androidx.compose.foundation.layout.BoxWithConstraintsScope  Rect :androidx.compose.foundation.layout.BoxWithConstraintsScope  apply :androidx.compose.foundation.layout.BoxWithConstraintsScope  clipPath :androidx.compose.foundation.layout.BoxWithConstraintsScope  listOf :androidx.compose.foundation.layout.BoxWithConstraintsScope  radialGradient :androidx.compose.foundation.layout.BoxWithConstraintsScope  scale :androidx.compose.foundation.layout.BoxWithConstraintsScope  	translate :androidx.compose.foundation.layout.BoxWithConstraintsScope  Green "androidx.compose.ui.graphics.Color  Green ,androidx.compose.ui.graphics.Color.Companion  Magenta "androidx.compose.ui.graphics.Color  Yellow "androidx.compose.ui.graphics.Color  Magenta ,androidx.compose.ui.graphics.Color.Companion  Yellow ,androidx.compose.ui.graphics.Color.Companion  openInputStream android.content.ContentResolver  contentResolver android.content.Context  Bitmap android.graphics  decodeStream android.graphics.BitmapFactory  	Exception "androidx.compose.foundation.layout  LaunchedEffect "androidx.compose.foundation.layout  
asImageBitmap "androidx.compose.foundation.layout  coerceAtMost "androidx.compose.foundation.layout  use "androidx.compose.foundation.layout  	Exception androidx.compose.material3  LaunchedEffect androidx.compose.material3  
asImageBitmap androidx.compose.material3  coerceAtMost androidx.compose.material3  use androidx.compose.material3  	Exception androidx.compose.runtime  LaunchedEffect androidx.compose.runtime  
asImageBitmap androidx.compose.runtime  coerceAtMost androidx.compose.runtime  use androidx.compose.runtime  clipToBounds androidx.compose.ui.draw  center androidx.compose.ui.geometry  	Companion #androidx.compose.ui.geometry.Offset  Zero #androidx.compose.ui.geometry.Offset  Zero -androidx.compose.ui.geometry.Offset.Companion  center !androidx.compose.ui.geometry.Size  	Exception androidx.compose.ui.graphics  LaunchedEffect androidx.compose.ui.graphics  android androidx.compose.ui.graphics  
asImageBitmap androidx.compose.ui.graphics  coerceAtMost androidx.compose.ui.graphics  use androidx.compose.ui.graphics  height (androidx.compose.ui.graphics.ImageBitmap  let (androidx.compose.ui.graphics.ImageBitmap  width (androidx.compose.ui.graphics.ImageBitmap  androidx !androidx.compose.ui.graphics.Path  coerceAtMost 0androidx.compose.ui.graphics.drawscope.DrawScope  	Exception com.example.zuijiji2  LaunchedEffect com.example.zuijiji2  
asImageBitmap com.example.zuijiji2  coerceAtMost com.example.zuijiji2  use com.example.zuijiji2  InputStream java.io  use java.io.InputStream  	Exception 	java.lang  printStackTrace java.lang.Exception  use kotlin  
coerceAtLeast 
kotlin.Int  coerceAtMost 
kotlin.Int  div 
kotlin.Int  printStackTrace kotlin.Throwable  use 	kotlin.io  coerceAtMost 
kotlin.ranges  CoroutineScope kotlinx.coroutines  android !kotlinx.coroutines.CoroutineScope  
asImageBitmap !kotlinx.coroutines.CoroutineScope  let !kotlinx.coroutines.CoroutineScope  use !kotlinx.coroutines.CoroutineScope  arrayOf "androidx.compose.foundation.layout  
component1 "androidx.compose.foundation.layout  
component2 "androidx.compose.foundation.layout  
component3 "androidx.compose.foundation.layout  
component4 "androidx.compose.foundation.layout  arrayOf androidx.compose.material3  
component1 androidx.compose.material3  
component2 androidx.compose.material3  
component3 androidx.compose.material3  
component4 androidx.compose.material3  arrayOf androidx.compose.runtime  
component1 androidx.compose.runtime  
component2 androidx.compose.runtime  
component3 androidx.compose.runtime  
component4 androidx.compose.runtime  arrayOf androidx.compose.ui.graphics  
component1 androidx.compose.ui.graphics  
component2 androidx.compose.ui.graphics  
component3 androidx.compose.ui.graphics  
component4 androidx.compose.ui.graphics  arrayOf 0androidx.compose.ui.graphics.drawscope.DrawScope  
component1 0androidx.compose.ui.graphics.drawscope.DrawScope  
component2 0androidx.compose.ui.graphics.drawscope.DrawScope  
component3 0androidx.compose.ui.graphics.drawscope.DrawScope  
component4 0androidx.compose.ui.graphics.drawscope.DrawScope  arrayOf com.example.zuijiji2  
component1 com.example.zuijiji2  
component2 com.example.zuijiji2  
component3 com.example.zuijiji2  
component4 com.example.zuijiji2  Array kotlin  arrayOf kotlin  
component1 kotlin.Array  
component2 kotlin.Array  
component3 kotlin.Array  
component4 kotlin.Array  coerceAtMost kotlin.Float  
component1 kotlin.collections  
component2 kotlin.collections  
component3 kotlin.collections  
component4 kotlin.collections  tertiary &androidx.compose.material3.ColorScheme  div androidx.compose.ui.unit.Dp  let androidx.compose.ui.Modifier  Boolean androidx.compose.ui.graphics  MagnifyingGlass +androidx.compose.foundation.layout.BoxScope  until "androidx.compose.foundation.layout  until +androidx.compose.foundation.layout.BoxScope  until :androidx.compose.foundation.layout.BoxWithConstraintsScope  until androidx.compose.material3  until androidx.compose.runtime  until androidx.compose.ui.graphics  until com.example.zuijiji2  minus 
kotlin.Double  	CharRange 
kotlin.ranges  	UIntRange 
kotlin.ranges  
ULongRange 
kotlin.ranges  until 
kotlin.ranges  
coerceAtLeast +androidx.compose.foundation.layout.BoxScope  
coerceAtLeast :androidx.compose.foundation.layout.BoxWithConstraintsScope  
graphicsLayer :androidx.compose.foundation.layout.BoxWithConstraintsScope  Int "androidx.compose.foundation.layout  OptimizedMultiLayerMagnifier "androidx.compose.foundation.layout  downTo "androidx.compose.foundation.layout  OptimizedMultiLayerMagnifier +androidx.compose.foundation.layout.BoxScope  OptimizedMultiLayerMagnifier :androidx.compose.foundation.layout.BoxWithConstraintsScope  Int androidx.compose.material3  OptimizedMultiLayerMagnifier androidx.compose.material3  downTo androidx.compose.material3  Int androidx.compose.runtime  OptimizedMultiLayerMagnifier androidx.compose.runtime  downTo androidx.compose.runtime  Int androidx.compose.ui.graphics  OptimizedMultiLayerMagnifier androidx.compose.ui.graphics  downTo androidx.compose.ui.graphics  downTo 0androidx.compose.ui.graphics.drawscope.DrawScope  Int com.example.zuijiji2  OptimizedMultiLayerMagnifier com.example.zuijiji2  downTo com.example.zuijiji2  downTo 
kotlin.Int  CharProgression 
kotlin.ranges  IntProgression 
kotlin.ranges  LongProgression 
kotlin.ranges  UIntProgression 
kotlin.ranges  ULongProgression 
kotlin.ranges  downTo 
kotlin.ranges  	DrawScope "androidx.compose.foundation.layout  ImageDisplayParams "androidx.compose.foundation.layout  OptimizedMagnifyingGlass "androidx.compose.foundation.layout  calculateImageDisplayParams "androidx.compose.foundation.layout  OptimizedMagnifyingGlass +androidx.compose.foundation.layout.BoxScope  OptimizedMagnifyingGlass :androidx.compose.foundation.layout.BoxWithConstraintsScope  	DrawScope androidx.compose.material3  ImageDisplayParams androidx.compose.material3  OptimizedMagnifyingGlass androidx.compose.material3  calculateImageDisplayParams androidx.compose.material3  	DrawScope androidx.compose.runtime  ImageDisplayParams androidx.compose.runtime  OptimizedMagnifyingGlass androidx.compose.runtime  calculateImageDisplayParams androidx.compose.runtime  clipToBounds androidx.compose.ui.Modifier  	DrawScope androidx.compose.ui.graphics  ImageDisplayParams androidx.compose.ui.graphics  OptimizedMagnifyingGlass androidx.compose.ui.graphics  calculateImageDisplayParams androidx.compose.ui.graphics  calculateImageDisplayParams 0androidx.compose.ui.graphics.drawscope.DrawScope  drawMagnifiedLayer 0androidx.compose.ui.graphics.drawscope.DrawScope  	DrawScope com.example.zuijiji2  ImageDisplayParams com.example.zuijiji2  OptimizedMagnifyingGlass com.example.zuijiji2  calculateImageDisplayParams com.example.zuijiji2  drawMagnifiedLayer com.example.zuijiji2  bitmapHeight 'com.example.zuijiji2.ImageDisplayParams  bitmapWidth 'com.example.zuijiji2.ImageDisplayParams  
displayHeight 'com.example.zuijiji2.ImageDisplayParams  displayWidth 'com.example.zuijiji2.ImageDisplayParams  let 'com.example.zuijiji2.ImageDisplayParams  offsetX 'com.example.zuijiji2.ImageDisplayParams  offsetY 'com.example.zuijiji2.ImageDisplayParams  abs "androidx.compose.foundation.layout  easeInOutCubic "androidx.compose.foundation.layout  pow "androidx.compose.foundation.layout  
smoothstep "androidx.compose.foundation.layout  abs androidx.compose.material3  easeInOutCubic androidx.compose.material3  pow androidx.compose.material3  
smoothstep androidx.compose.material3  abs androidx.compose.runtime  easeInOutCubic androidx.compose.runtime  pow androidx.compose.runtime  
smoothstep androidx.compose.runtime  abs androidx.compose.ui.graphics  easeInOutCubic androidx.compose.ui.graphics  pow androidx.compose.ui.graphics  
smoothstep androidx.compose.ui.graphics  abs 0androidx.compose.ui.graphics.drawscope.DrawScope  coerceIn 0androidx.compose.ui.graphics.drawscope.DrawScope  drawMagnifiedLayerWithAlpha 0androidx.compose.ui.graphics.drawscope.DrawScope  easeInOutCubic 0androidx.compose.ui.graphics.drawscope.DrawScope  pow 0androidx.compose.ui.graphics.drawscope.DrawScope  
smoothstep 0androidx.compose.ui.graphics.drawscope.DrawScope  abs com.example.zuijiji2  drawMagnifiedLayerWithAlpha com.example.zuijiji2  easeInOutCubic com.example.zuijiji2  pow com.example.zuijiji2  
smoothstep com.example.zuijiji2  pow kotlin.Float  abs kotlin.math  pow kotlin.math  let android.graphics.Bitmap  GLES30 android.opengl  
GLSurfaceView android.opengl  GLUtils android.opengl  GL_ARRAY_BUFFER android.opengl.GLES20  GL_BLEND android.opengl.GLES20  GL_CLAMP_TO_EDGE android.opengl.GLES20  GL_COLOR_BUFFER_BIT android.opengl.GLES20  GL_COMPILE_STATUS android.opengl.GLES20  GL_ELEMENT_ARRAY_BUFFER android.opengl.GLES20  GL_FLOAT android.opengl.GLES20  GL_FRAGMENT_SHADER android.opengl.GLES20  	GL_LINEAR android.opengl.GLES20  GL_LINK_STATUS android.opengl.GLES20  GL_NO_ERROR android.opengl.GLES20  GL_ONE_MINUS_SRC_ALPHA android.opengl.GLES20  GL_SRC_ALPHA android.opengl.GLES20  GL_STATIC_DRAW android.opengl.GLES20  GL_TEXTURE0 android.opengl.GLES20  
GL_TEXTURE_2D android.opengl.GLES20  GL_TEXTURE_MAG_FILTER android.opengl.GLES20  GL_TEXTURE_MIN_FILTER android.opengl.GLES20  GL_TEXTURE_WRAP_S android.opengl.GLES20  GL_TEXTURE_WRAP_T android.opengl.GLES20  GL_TRIANGLES android.opengl.GLES20  GL_UNSIGNED_INT android.opengl.GLES20  GL_VERTEX_SHADER android.opengl.GLES20  glActiveTexture android.opengl.GLES20  glAttachShader android.opengl.GLES20  glBindBuffer android.opengl.GLES20  
glBindTexture android.opengl.GLES20  glBlendFunc android.opengl.GLES20  glBufferData android.opengl.GLES20  glClear android.opengl.GLES20  glClearColor android.opengl.GLES20  glCompileShader android.opengl.GLES20  glCreateProgram android.opengl.GLES20  glCreateShader android.opengl.GLES20  glDeleteBuffers android.opengl.GLES20  glDeleteProgram android.opengl.GLES20  glDeleteShader android.opengl.GLES20  glDeleteTextures android.opengl.GLES20  glDrawElements android.opengl.GLES20  glEnable android.opengl.GLES20  glEnableVertexAttribArray android.opengl.GLES20  glGenBuffers android.opengl.GLES20  
glGenTextures android.opengl.GLES20  
glGetError android.opengl.GLES20  glGetProgramInfoLog android.opengl.GLES20  glGetProgramiv android.opengl.GLES20  glGetShaderInfoLog android.opengl.GLES20  
glGetShaderiv android.opengl.GLES20  glGetUniformLocation android.opengl.GLES20  
glLinkProgram android.opengl.GLES20  glShaderSource android.opengl.GLES20  glTexParameteri android.opengl.GLES20  glUniform1f android.opengl.GLES20  glUniform1i android.opengl.GLES20  glUniform2f android.opengl.GLES20  glUniform4f android.opengl.GLES20  glUseProgram android.opengl.GLES20  glVertexAttribPointer android.opengl.GLES20  
glViewport android.opengl.GLES20  GL_ARRAY_BUFFER android.opengl.GLES30  GL_BLEND android.opengl.GLES30  GL_CLAMP_TO_EDGE android.opengl.GLES30  GL_COLOR_BUFFER_BIT android.opengl.GLES30  GL_COMPILE_STATUS android.opengl.GLES30  GL_ELEMENT_ARRAY_BUFFER android.opengl.GLES30  GL_FLOAT android.opengl.GLES30  GL_FRAGMENT_SHADER android.opengl.GLES30  	GL_LINEAR android.opengl.GLES30  GL_LINK_STATUS android.opengl.GLES30  GL_NO_ERROR android.opengl.GLES30  GL_ONE_MINUS_SRC_ALPHA android.opengl.GLES30  GL_SRC_ALPHA android.opengl.GLES30  GL_STATIC_DRAW android.opengl.GLES30  GL_TEXTURE0 android.opengl.GLES30  
GL_TEXTURE_2D android.opengl.GLES30  GL_TEXTURE_MAG_FILTER android.opengl.GLES30  GL_TEXTURE_MIN_FILTER android.opengl.GLES30  GL_TEXTURE_WRAP_S android.opengl.GLES30  GL_TEXTURE_WRAP_T android.opengl.GLES30  GL_TRIANGLES android.opengl.GLES30  GL_UNSIGNED_INT android.opengl.GLES30  GL_VERTEX_SHADER android.opengl.GLES30  glActiveTexture android.opengl.GLES30  glAttachShader android.opengl.GLES30  glBindBuffer android.opengl.GLES30  
glBindTexture android.opengl.GLES30  glBindVertexArray android.opengl.GLES30  glBlendFunc android.opengl.GLES30  glBufferData android.opengl.GLES30  glClear android.opengl.GLES30  glClearColor android.opengl.GLES30  glCompileShader android.opengl.GLES30  glCreateProgram android.opengl.GLES30  glCreateShader android.opengl.GLES30  glDeleteBuffers android.opengl.GLES30  glDeleteProgram android.opengl.GLES30  glDeleteShader android.opengl.GLES30  glDeleteTextures android.opengl.GLES30  glDeleteVertexArrays android.opengl.GLES30  glDrawElements android.opengl.GLES30  glEnable android.opengl.GLES30  glEnableVertexAttribArray android.opengl.GLES30  glGenBuffers android.opengl.GLES30  
glGenTextures android.opengl.GLES30  glGenVertexArrays android.opengl.GLES30  
glGetError android.opengl.GLES30  glGetProgramInfoLog android.opengl.GLES30  glGetProgramiv android.opengl.GLES30  glGetShaderInfoLog android.opengl.GLES30  
glGetShaderiv android.opengl.GLES30  glGetUniformLocation android.opengl.GLES30  
glLinkProgram android.opengl.GLES30  glShaderSource android.opengl.GLES30  glTexParameteri android.opengl.GLES30  glUniform1f android.opengl.GLES30  glUniform1i android.opengl.GLES30  glUniform2f android.opengl.GLES30  glUniform4f android.opengl.GLES30  glUseProgram android.opengl.GLES30  glVertexAttribPointer android.opengl.GLES30  
glViewport android.opengl.GLES30  LiquidGlassRenderer android.opengl.GLSurfaceView  Log android.opengl.GLSurfaceView  MotionEvent android.opengl.GLSurfaceView  RENDERMODE_CONTINUOUSLY android.opengl.GLSurfaceView  Renderer android.opengl.GLSurfaceView  ScaleGestureDetector android.opengl.GLSurfaceView  TAG android.opengl.GLSurfaceView  	isScaling android.opengl.GLSurfaceView  max android.opengl.GLSurfaceView  min android.opengl.GLSurfaceView  onPause android.opengl.GLSurfaceView  onResume android.opengl.GLSurfaceView  onTouchEvent android.opengl.GLSurfaceView  
queueEvent android.opengl.GLSurfaceView  
renderMode android.opengl.GLSurfaceView  renderer android.opengl.GLSurfaceView  scaleFactor android.opengl.GLSurfaceView  setEGLContextClientVersion android.opengl.GLSurfaceView  setRenderer android.opengl.GLSurfaceView  timesAssign android.opengl.GLSurfaceView  
texImage2D android.opengl.GLUtils  AttributeSet android.util  Log android.util  d android.util.Log  e android.util.Log  MotionEvent android.view  ScaleGestureDetector android.view  
ACTION_CANCEL android.view.MotionEvent  ACTION_DOWN android.view.MotionEvent  ACTION_MOVE android.view.MotionEvent  	ACTION_UP android.view.MotionEvent  action android.view.MotionEvent  x android.view.MotionEvent  y android.view.MotionEvent  SimpleOnScaleGestureListener !android.view.ScaleGestureDetector  onTouchEvent !android.view.ScaleGestureDetector  scaleFactor !android.view.ScaleGestureDetector  Log >android.view.ScaleGestureDetector.SimpleOnScaleGestureListener  TAG >android.view.ScaleGestureDetector.SimpleOnScaleGestureListener  	isScaling >android.view.ScaleGestureDetector.SimpleOnScaleGestureListener  max >android.view.ScaleGestureDetector.SimpleOnScaleGestureListener  min >android.view.ScaleGestureDetector.SimpleOnScaleGestureListener  
queueEvent >android.view.ScaleGestureDetector.SimpleOnScaleGestureListener  renderer >android.view.ScaleGestureDetector.SimpleOnScaleGestureListener  scaleFactor >android.view.ScaleGestureDetector.SimpleOnScaleGestureListener  timesAssign >android.view.ScaleGestureDetector.SimpleOnScaleGestureListener  LiquidGlassRenderer android.view.SurfaceView  Log android.view.SurfaceView  MotionEvent android.view.SurfaceView  RENDERMODE_CONTINUOUSLY android.view.SurfaceView  ScaleGestureDetector android.view.SurfaceView  TAG android.view.SurfaceView  	isScaling android.view.SurfaceView  max android.view.SurfaceView  min android.view.SurfaceView  
queueEvent android.view.SurfaceView  renderer android.view.SurfaceView  scaleFactor android.view.SurfaceView  timesAssign android.view.SurfaceView  LiquidGlassRenderer android.view.View  Log android.view.View  MotionEvent android.view.View  RENDERMODE_CONTINUOUSLY android.view.View  ScaleGestureDetector android.view.View  TAG android.view.View  	isScaling android.view.View  max android.view.View  min android.view.View  onTouchEvent android.view.View  
queueEvent android.view.View  renderer android.view.View  scaleFactor android.view.View  timesAssign android.view.View  AndroidView "androidx.compose.foundation.layout  LiquidGlassView "androidx.compose.foundation.layout  also "androidx.compose.foundation.layout  AndroidView :androidx.compose.foundation.layout.BoxWithConstraintsScope  Column :androidx.compose.foundation.layout.BoxWithConstraintsScope  LiquidGlassView :androidx.compose.foundation.layout.BoxWithConstraintsScope  also :androidx.compose.foundation.layout.BoxWithConstraintsScope  android :androidx.compose.foundation.layout.BoxWithConstraintsScope  height :androidx.compose.foundation.layout.BoxWithConstraintsScope  use :androidx.compose.foundation.layout.BoxWithConstraintsScope  Button .androidx.compose.foundation.layout.ColumnScope  ButtonDefaults .androidx.compose.foundation.layout.ColumnScope  buttonColors .androidx.compose.foundation.layout.ColumnScope  fillMaxWidth .androidx.compose.foundation.layout.ColumnScope  width .androidx.compose.foundation.layout.ColumnScope  AndroidView androidx.compose.material3  LiquidGlassView androidx.compose.material3  also androidx.compose.material3  outline &androidx.compose.material3.ColorScheme  AndroidView androidx.compose.runtime  LiquidGlassView androidx.compose.runtime  also androidx.compose.runtime  fillMaxWidth &androidx.compose.ui.Modifier.Companion  AndroidView androidx.compose.ui.graphics  Column androidx.compose.ui.graphics  LiquidGlassView androidx.compose.ui.graphics  also androidx.compose.ui.graphics  height androidx.compose.ui.graphics  AndroidView androidx.compose.ui.viewinterop  AndroidView com.example.zuijiji2  LiquidGlassView com.example.zuijiji2  also com.example.zuijiji2  AttributeSet com.example.zuijiji2.opengl  Bitmap com.example.zuijiji2.opengl  Boolean com.example.zuijiji2.opengl  
ByteBuffer com.example.zuijiji2.opengl  	ByteOrder com.example.zuijiji2.opengl  Context com.example.zuijiji2.opengl  	EGLConfig com.example.zuijiji2.opengl  Float com.example.zuijiji2.opengl  GL10 com.example.zuijiji2.opengl  GLES30 com.example.zuijiji2.opengl  
GLSurfaceView com.example.zuijiji2.opengl  GLUtils com.example.zuijiji2.opengl  Int com.example.zuijiji2.opengl  IntArray com.example.zuijiji2.opengl  JvmOverloads com.example.zuijiji2.opengl  LiquidGlassRenderer com.example.zuijiji2.opengl  LiquidGlassShaders com.example.zuijiji2.opengl  LiquidGlassView com.example.zuijiji2.opengl  Log com.example.zuijiji2.opengl  MotionEvent com.example.zuijiji2.opengl  Pair com.example.zuijiji2.opengl  RENDERMODE_CONTINUOUSLY com.example.zuijiji2.opengl  ScaleGestureDetector com.example.zuijiji2.opengl  String com.example.zuijiji2.opengl  System com.example.zuijiji2.opengl  TAG com.example.zuijiji2.opengl  Triple com.example.zuijiji2.opengl  checkGLError com.example.zuijiji2.opengl  coerceIn com.example.zuijiji2.opengl  cos com.example.zuijiji2.opengl  
createProgram com.example.zuijiji2.opengl  createQuadBuffers com.example.zuijiji2.opengl  
createTexture com.example.zuijiji2.opengl  floatArrayOf com.example.zuijiji2.opengl  
intArrayOf com.example.zuijiji2.opengl  	isScaling com.example.zuijiji2.opengl  let com.example.zuijiji2.opengl  max com.example.zuijiji2.opengl  min com.example.zuijiji2.opengl  
plusAssign com.example.zuijiji2.opengl  
queueEvent com.example.zuijiji2.opengl  renderer com.example.zuijiji2.opengl  scaleFactor com.example.zuijiji2.opengl  sin com.example.zuijiji2.opengl  timesAssign com.example.zuijiji2.opengl  Renderer )com.example.zuijiji2.opengl.GLSurfaceView  
ByteBuffer #com.example.zuijiji2.opengl.GLUtils  	ByteOrder #com.example.zuijiji2.opengl.GLUtils  GLES30 #com.example.zuijiji2.opengl.GLUtils  GLUtils #com.example.zuijiji2.opengl.GLUtils  IntArray #com.example.zuijiji2.opengl.GLUtils  Log #com.example.zuijiji2.opengl.GLUtils  TAG #com.example.zuijiji2.opengl.GLUtils  Triple #com.example.zuijiji2.opengl.GLUtils  checkGLError #com.example.zuijiji2.opengl.GLUtils  
compileShader #com.example.zuijiji2.opengl.GLUtils  
createProgram #com.example.zuijiji2.opengl.GLUtils  createQuadBuffers #com.example.zuijiji2.opengl.GLUtils  
createTexture #com.example.zuijiji2.opengl.GLUtils  floatArrayOf #com.example.zuijiji2.opengl.GLUtils  
intArrayOf #com.example.zuijiji2.opengl.GLUtils  GLES30 /com.example.zuijiji2.opengl.LiquidGlassRenderer  GLUtils /com.example.zuijiji2.opengl.LiquidGlassRenderer  LiquidGlassShaders /com.example.zuijiji2.opengl.LiquidGlassRenderer  Log /com.example.zuijiji2.opengl.LiquidGlassRenderer  Pair /com.example.zuijiji2.opengl.LiquidGlassRenderer  System /com.example.zuijiji2.opengl.LiquidGlassRenderer  TAG /com.example.zuijiji2.opengl.LiquidGlassRenderer  backgroundBitmap /com.example.zuijiji2.opengl.LiquidGlassRenderer  backgroundTexture /com.example.zuijiji2.opengl.LiquidGlassRenderer  backgroundTextureLoc /com.example.zuijiji2.opengl.LiquidGlassRenderer  checkGLError /com.example.zuijiji2.opengl.LiquidGlassRenderer  cleanup /com.example.zuijiji2.opengl.LiquidGlassRenderer  coerceIn /com.example.zuijiji2.opengl.LiquidGlassRenderer  	contrlLoc /com.example.zuijiji2.opengl.LiquidGlassRenderer  cos /com.example.zuijiji2.opengl.LiquidGlassRenderer  createBackgroundTexture /com.example.zuijiji2.opengl.LiquidGlassRenderer  
createProgram /com.example.zuijiji2.opengl.LiquidGlassRenderer  createQuadBuffers /com.example.zuijiji2.opengl.LiquidGlassRenderer  
createTexture /com.example.zuijiji2.opengl.LiquidGlassRenderer  ebo /com.example.zuijiji2.opengl.LiquidGlassRenderer  	glassBlur /com.example.zuijiji2.opengl.LiquidGlassRenderer  glassBlurLoc /com.example.zuijiji2.opengl.LiquidGlassRenderer  glassCenter /com.example.zuijiji2.opengl.LiquidGlassRenderer  glassCenterLoc /com.example.zuijiji2.opengl.LiquidGlassRenderer  
glassColorLoc /com.example.zuijiji2.opengl.LiquidGlassRenderer  glassRadius /com.example.zuijiji2.opengl.LiquidGlassRenderer  glassRadiusLoc /com.example.zuijiji2.opengl.LiquidGlassRenderer  
intArrayOf /com.example.zuijiji2.opengl.LiquidGlassRenderer  let /com.example.zuijiji2.opengl.LiquidGlassRenderer  lightPosLoc /com.example.zuijiji2.opengl.LiquidGlassRenderer  max /com.example.zuijiji2.opengl.LiquidGlassRenderer  min /com.example.zuijiji2.opengl.LiquidGlassRenderer  
plusAssign /com.example.zuijiji2.opengl.LiquidGlassRenderer  
resolutionLoc /com.example.zuijiji2.opengl.LiquidGlassRenderer  screenHeight /com.example.zuijiji2.opengl.LiquidGlassRenderer  screenWidth /com.example.zuijiji2.opengl.LiquidGlassRenderer  setBackgroundBitmap /com.example.zuijiji2.opengl.LiquidGlassRenderer  
shaderProgram /com.example.zuijiji2.opengl.LiquidGlassRenderer  sin /com.example.zuijiji2.opengl.LiquidGlassRenderer  	startTime /com.example.zuijiji2.opengl.LiquidGlassRenderer  targetGlassRadius /com.example.zuijiji2.opengl.LiquidGlassRenderer  updateGlassCenter /com.example.zuijiji2.opengl.LiquidGlassRenderer  updateGlassRadius /com.example.zuijiji2.opengl.LiquidGlassRenderer  vao /com.example.zuijiji2.opengl.LiquidGlassRenderer  vbo /com.example.zuijiji2.opengl.LiquidGlassRenderer  zoomLoc /com.example.zuijiji2.opengl.LiquidGlassRenderer  FRAGMENT_SHADER .com.example.zuijiji2.opengl.LiquidGlassShaders  
VERTEX_SHADER .com.example.zuijiji2.opengl.LiquidGlassShaders  AttributeSet +com.example.zuijiji2.opengl.LiquidGlassView  Bitmap +com.example.zuijiji2.opengl.LiquidGlassView  Boolean +com.example.zuijiji2.opengl.LiquidGlassView  Context +com.example.zuijiji2.opengl.LiquidGlassView  JvmOverloads +com.example.zuijiji2.opengl.LiquidGlassView  LiquidGlassRenderer +com.example.zuijiji2.opengl.LiquidGlassView  Log +com.example.zuijiji2.opengl.LiquidGlassView  MotionEvent +com.example.zuijiji2.opengl.LiquidGlassView  RENDERMODE_CONTINUOUSLY +com.example.zuijiji2.opengl.LiquidGlassView  ScaleGestureDetector +com.example.zuijiji2.opengl.LiquidGlassView  
ScaleListener +com.example.zuijiji2.opengl.LiquidGlassView  TAG +com.example.zuijiji2.opengl.LiquidGlassView  also +com.example.zuijiji2.opengl.LiquidGlassView  	isScaling +com.example.zuijiji2.opengl.LiquidGlassView  
lastTouchX +com.example.zuijiji2.opengl.LiquidGlassView  
lastTouchY +com.example.zuijiji2.opengl.LiquidGlassView  let +com.example.zuijiji2.opengl.LiquidGlassView  max +com.example.zuijiji2.opengl.LiquidGlassView  min +com.example.zuijiji2.opengl.LiquidGlassView  
queueEvent +com.example.zuijiji2.opengl.LiquidGlassView  
renderMode +com.example.zuijiji2.opengl.LiquidGlassView  renderer +com.example.zuijiji2.opengl.LiquidGlassView  scaleFactor +com.example.zuijiji2.opengl.LiquidGlassView  scaleGestureDetector +com.example.zuijiji2.opengl.LiquidGlassView  setBackgroundBitmap +com.example.zuijiji2.opengl.LiquidGlassView  setEGLContextClientVersion +com.example.zuijiji2.opengl.LiquidGlassView  setRenderer +com.example.zuijiji2.opengl.LiquidGlassView  timesAssign +com.example.zuijiji2.opengl.LiquidGlassView  SimpleOnScaleGestureListener @com.example.zuijiji2.opengl.LiquidGlassView.ScaleGestureDetector  Log 9com.example.zuijiji2.opengl.LiquidGlassView.ScaleListener  TAG 9com.example.zuijiji2.opengl.LiquidGlassView.ScaleListener  	isScaling 9com.example.zuijiji2.opengl.LiquidGlassView.ScaleListener  max 9com.example.zuijiji2.opengl.LiquidGlassView.ScaleListener  min 9com.example.zuijiji2.opengl.LiquidGlassView.ScaleListener  
queueEvent 9com.example.zuijiji2.opengl.LiquidGlassView.ScaleListener  renderer 9com.example.zuijiji2.opengl.LiquidGlassView.ScaleListener  scaleFactor 9com.example.zuijiji2.opengl.LiquidGlassView.ScaleListener  timesAssign 9com.example.zuijiji2.opengl.LiquidGlassView.ScaleListener  SimpleOnScaleGestureListener 0com.example.zuijiji2.opengl.ScaleGestureDetector  Runnable 	java.lang  <SAM-CONSTRUCTOR> java.lang.Runnable  currentTimeMillis java.lang.System  
ByteBuffer java.nio  	ByteOrder java.nio  FloatBuffer java.nio  	IntBuffer java.nio  position java.nio.Buffer  allocateDirect java.nio.ByteBuffer  
asFloatBuffer java.nio.ByteBuffer  asIntBuffer java.nio.ByteBuffer  order java.nio.ByteBuffer  nativeOrder java.nio.ByteOrder  position java.nio.FloatBuffer  put java.nio.FloatBuffer  position java.nio.IntBuffer  put java.nio.IntBuffer  	EGLConfig javax.microedition.khronos.egl  GL10 #javax.microedition.khronos.opengles  
FloatArray kotlin  IntArray kotlin  Pair kotlin  Triple kotlin  also kotlin  floatArrayOf kotlin  
intArrayOf kotlin  
plusAssign kotlin.Float  timesAssign kotlin.Float  size kotlin.FloatArray  get kotlin.IntArray  size kotlin.IntArray  div kotlin.Long  minus kotlin.Long  first kotlin.Pair  second kotlin.Pair  
component1 
kotlin.Triple  
component2 
kotlin.Triple  
component3 
kotlin.Triple  
plusAssign kotlin.collections  JvmOverloads 
kotlin.jvm  Bitmap kotlin.math  	EGLConfig kotlin.math  Float kotlin.math  GL10 kotlin.math  GLES30 kotlin.math  
GLSurfaceView kotlin.math  GLUtils kotlin.math  Int kotlin.math  LiquidGlassShaders kotlin.math  Log kotlin.math  Pair kotlin.math  System kotlin.math  checkGLError kotlin.math  coerceIn kotlin.math  
createProgram kotlin.math  createQuadBuffers kotlin.math  
createTexture kotlin.math  
intArrayOf kotlin.math  let kotlin.math  
plusAssign kotlin.math  Renderer kotlin.math.GLSurfaceView  Config android.graphics.Bitmap  createBitmap android.graphics.Bitmap  recycle android.graphics.Bitmap  setPixel android.graphics.Bitmap  	ARGB_8888 android.graphics.Bitmap.Config  until com.example.zuijiji2.opengl  Bitmap /com.example.zuijiji2.opengl.LiquidGlassRenderer  createDefaultTexture /com.example.zuijiji2.opengl.LiquidGlassRenderer  until /com.example.zuijiji2.opengl.LiquidGlassRenderer  or 
kotlin.Int  shl 
kotlin.Int  until kotlin.math  height android.graphics.Bitmap  width android.graphics.Bitmap  SimpleTestShaders com.example.zuijiji2.opengl  SimpleTestShaders /com.example.zuijiji2.opengl.LiquidGlassRenderer  FRAGMENT_SHADER -com.example.zuijiji2.opengl.SimpleTestShaders  
VERTEX_SHADER -com.example.zuijiji2.opengl.SimpleTestShaders  SimpleTestShaders kotlin.math  LaunchedEffect :androidx.compose.foundation.layout.BoxWithConstraintsScope  surfaceVariant &androidx.compose.material3.ColorScheme  	Exception android.opengl.GLSurfaceView  w android.util.Log  	Exception android.view.SurfaceView  	Exception android.view.View  	Exception com.example.zuijiji2.opengl  StableLiquidGlassShaders com.example.zuijiji2.opengl  StableLiquidGlassShaders /com.example.zuijiji2.opengl.LiquidGlassRenderer  	Exception +com.example.zuijiji2.opengl.LiquidGlassView  FRAGMENT_SHADER 4com.example.zuijiji2.opengl.StableLiquidGlassShaders  
VERTEX_SHADER 4com.example.zuijiji2.opengl.StableLiquidGlassShaders  	Exception kotlin.math  StableLiquidGlassShaders kotlin.math  
requestRender android.opengl.GLSurfaceView  scaleStartX android.opengl.GLSurfaceView  scaleStartY android.opengl.GLSurfaceView  pointerCount android.view.MotionEvent  focusX !android.view.ScaleGestureDetector  focusY !android.view.ScaleGestureDetector  
requestRender >android.view.ScaleGestureDetector.SimpleOnScaleGestureListener  scaleStartX >android.view.ScaleGestureDetector.SimpleOnScaleGestureListener  scaleStartY >android.view.ScaleGestureDetector.SimpleOnScaleGestureListener  
requestRender android.view.SurfaceView  scaleStartX android.view.SurfaceView  scaleStartY android.view.SurfaceView  
requestRender android.view.View  scaleStartX android.view.View  scaleStartY android.view.View  
requestRender com.example.zuijiji2.opengl  scaleStartX com.example.zuijiji2.opengl  scaleStartY com.example.zuijiji2.opengl  
requestRender +com.example.zuijiji2.opengl.LiquidGlassView  scaleStartX +com.example.zuijiji2.opengl.LiquidGlassView  scaleStartY +com.example.zuijiji2.opengl.LiquidGlassView  
requestRender 9com.example.zuijiji2.opengl.LiquidGlassView.ScaleListener  scaleStartX 9com.example.zuijiji2.opengl.LiquidGlassView.ScaleListener  scaleStartY 9com.example.zuijiji2.opengl.LiquidGlassView.ScaleListener  
dispersionLoc /com.example.zuijiji2.opengl.LiquidGlassRenderer  	bodySmall %androidx.compose.material3.Typography  glassColorEnabled /com.example.zuijiji2.opengl.LiquidGlassRenderer  setGlassColorEnabled /com.example.zuijiji2.opengl.LiquidGlassRenderer  setGlassColorEnabled +com.example.zuijiji2.opengl.LiquidGlassView  Boolean kotlin.math  glassSizeLoc /com.example.zuijiji2.opengl.LiquidGlassRenderer  glassCornerRadiusLoc /com.example.zuijiji2.opengl.LiquidGlassRenderer  Slider "androidx.compose.foundation.layout  rangeTo "androidx.compose.foundation.layout  AndroidView +androidx.compose.foundation.layout.BoxScope  LiquidGlassView +androidx.compose.foundation.layout.BoxScope  Slider +androidx.compose.foundation.layout.BoxScope  also +androidx.compose.foundation.layout.BoxScope  android +androidx.compose.foundation.layout.BoxScope  rangeTo +androidx.compose.foundation.layout.BoxScope  use +androidx.compose.foundation.layout.BoxScope  Slider :androidx.compose.foundation.layout.BoxWithConstraintsScope  rangeTo :androidx.compose.foundation.layout.BoxWithConstraintsScope  Slider .androidx.compose.foundation.layout.ColumnScope  rangeTo .androidx.compose.foundation.layout.ColumnScope  Slider androidx.compose.material3  rangeTo androidx.compose.material3  error &androidx.compose.material3.ColorScheme  
titleSmall %androidx.compose.material3.Typography  Slider androidx.compose.runtime  rangeTo androidx.compose.runtime  TopEnd androidx.compose.ui.Alignment  TopEnd 'androidx.compose.ui.Alignment.Companion  Slider androidx.compose.ui.graphics  rangeTo androidx.compose.ui.graphics  Slider com.example.zuijiji2  rangeTo com.example.zuijiji2  customGlassHeight /com.example.zuijiji2.opengl.LiquidGlassRenderer  customGlassWidth /com.example.zuijiji2.opengl.LiquidGlassRenderer  setCustomGlassSize /com.example.zuijiji2.opengl.LiquidGlassRenderer  Float +com.example.zuijiji2.opengl.LiquidGlassView  setCustomGlassSize +com.example.zuijiji2.opengl.LiquidGlassView  rangeTo kotlin.Float  ClosedFloatingPointRange 
kotlin.ranges  ClosedRange 
kotlin.ranges  rangeTo 
kotlin.ranges  
isDragging android.opengl.GLSurfaceView  kotlin android.opengl.GLSurfaceView  
isDragging >android.view.ScaleGestureDetector.SimpleOnScaleGestureListener  
isDragging android.view.SurfaceView  kotlin android.view.SurfaceView  
isDragging android.view.View  kotlin android.view.View  
isDragging com.example.zuijiji2.opengl  kotlin com.example.zuijiji2.opengl  
dragStartX +com.example.zuijiji2.opengl.LiquidGlassView  
dragStartY +com.example.zuijiji2.opengl.LiquidGlassView  
isDragging +com.example.zuijiji2.opengl.LiquidGlassView  kotlin +com.example.zuijiji2.opengl.LiquidGlassView  
isDragging 9com.example.zuijiji2.opengl.LiquidGlassView.ScaleListener  kotlin 
kotlin.jvm  KClass kotlin.reflect  coerceIn android.opengl.GLSurfaceView  
glassCurrentX android.opengl.GLSurfaceView  
glassCurrentY android.opengl.GLSurfaceView  glassInitialized android.opengl.GLSurfaceView  
plusAssign android.opengl.GLSurfaceView  
glassCurrentX >android.view.ScaleGestureDetector.SimpleOnScaleGestureListener  
glassCurrentY >android.view.ScaleGestureDetector.SimpleOnScaleGestureListener  glassInitialized >android.view.ScaleGestureDetector.SimpleOnScaleGestureListener  coerceIn android.view.SurfaceView  
glassCurrentX android.view.SurfaceView  
glassCurrentY android.view.SurfaceView  glassInitialized android.view.SurfaceView  
plusAssign android.view.SurfaceView  coerceIn android.view.View  
glassCurrentX android.view.View  
glassCurrentY android.view.View  glassInitialized android.view.View  height android.view.View  
plusAssign android.view.View  width android.view.View  
glassCurrentX com.example.zuijiji2.opengl  
glassCurrentY com.example.zuijiji2.opengl  glassInitialized com.example.zuijiji2.opengl  coerceIn +com.example.zuijiji2.opengl.LiquidGlassView  
glassCurrentX +com.example.zuijiji2.opengl.LiquidGlassView  
glassCurrentY +com.example.zuijiji2.opengl.LiquidGlassView  glassInitialized +com.example.zuijiji2.opengl.LiquidGlassView  height +com.example.zuijiji2.opengl.LiquidGlassView  
plusAssign +com.example.zuijiji2.opengl.LiquidGlassView  width +com.example.zuijiji2.opengl.LiquidGlassView  
glassCurrentX 9com.example.zuijiji2.opengl.LiquidGlassView.ScaleListener  
glassCurrentY 9com.example.zuijiji2.opengl.LiquidGlassView.ScaleListener  glassInitialized 9com.example.zuijiji2.opengl.LiquidGlassView.ScaleListener  sp :androidx.compose.foundation.layout.BoxWithConstraintsScope  
headlineLarge %androidx.compose.material3.Typography  copy "androidx.compose.ui.text.TextStyle                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              