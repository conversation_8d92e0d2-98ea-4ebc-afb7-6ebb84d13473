package com.example.zuijiji2.opengl

import android.graphics.Bitmap
import android.opengl.GLES30
import android.opengl.GLSurfaceView
import android.util.Log
import javax.microedition.khronos.egl.EGLConfig
import javax.microedition.khronos.opengles.GL10
import kotlin.math.*

class LiquidGlassRenderer : GLSurfaceView.Renderer {
    private val TAG = "LiquidGlassRenderer"
    
    // OpenGL 对象
    private var shaderProgram = 0
    private var vao = 0
    private var vbo = 0
    private var ebo = 0
    private var backgroundTexture = 0
    
    // Uniform 位置
    private var glassCenterLoc = 0
    private var glassRadiusLoc = 0
    private var glassBlurLoc = 0
    private var glassColorLoc = 0
    private var lightPosLoc = 0
    private var contrlLoc = 0
    private var zoomLoc = 0
    private var resolutionLoc = 0
    private var backgroundTextureLoc = 0
    private var glassSizeLoc = 0
    private var glassCornerRadiusLoc = 0
    
    // 玻璃参数
    private var glassCenter = Pair(0.5f, 0.5f) // 归一化坐标
    private var glassRadius = 0.15f // 归一化半径
    private var glassBlur = 0.075f
    private var targetGlassRadius = 0.15f
    
    // 屏幕尺寸
    private var screenWidth = 1
    private var screenHeight = 1
    
    // 动画参数
    private var startTime = System.currentTimeMillis()
    
    // 背景位图
    private var backgroundBitmap: Bitmap? = null
    
    override fun onSurfaceCreated(gl: GL10?, config: EGLConfig?) {
        Log.d(TAG, "onSurfaceCreated")
        
        // 设置清除颜色
        GLES30.glClearColor(0.0f, 0.0f, 0.0f, 1.0f)
        
        // 启用混合
        GLES30.glEnable(GLES30.GL_BLEND)
        GLES30.glBlendFunc(GLES30.GL_SRC_ALPHA, GLES30.GL_ONE_MINUS_SRC_ALPHA)
        
        // 创建着色器程序 - 使用完整的液体玻璃效果
        shaderProgram = GLUtils.createProgram(
            LiquidGlassShaders.VERTEX_SHADER,
            LiquidGlassShaders.FRAGMENT_SHADER
        )

        if (shaderProgram == 0) {
            Log.e(TAG, "Failed to create shader program")
            return
        } else {
            Log.d(TAG, "Shader program created successfully: $shaderProgram")
        }
        
        // 获取uniform位置
        glassCenterLoc = GLES30.glGetUniformLocation(shaderProgram, "glassCenter")
        glassRadiusLoc = GLES30.glGetUniformLocation(shaderProgram, "glassRadius")
        glassBlurLoc = GLES30.glGetUniformLocation(shaderProgram, "glassBlur")
        glassColorLoc = GLES30.glGetUniformLocation(shaderProgram, "glassColor")
        lightPosLoc = GLES30.glGetUniformLocation(shaderProgram, "lightPos")
        contrlLoc = GLES30.glGetUniformLocation(shaderProgram, "contrl")
        zoomLoc = GLES30.glGetUniformLocation(shaderProgram, "zoom")
        resolutionLoc = GLES30.glGetUniformLocation(shaderProgram, "resolution")
        backgroundTextureLoc = GLES30.glGetUniformLocation(shaderProgram, "backgroundTexture")
        glassSizeLoc = GLES30.glGetUniformLocation(shaderProgram, "glassSize")
        glassCornerRadiusLoc = GLES30.glGetUniformLocation(shaderProgram, "glassCornerRadius")

        Log.d(TAG, "Uniform locations - glassCenter: $glassCenterLoc, glassRadius: $glassRadiusLoc, glassSize: $glassSizeLoc, resolution: $resolutionLoc, backgroundTexture: $backgroundTextureLoc")
        
        // 创建全屏四边形
        val (vaoId, vboId, eboId) = GLUtils.createQuadBuffers()
        vao = vaoId
        vbo = vboId
        ebo = eboId
        
        GLUtils.checkGLError("onSurfaceCreated")
    }
    
    override fun onSurfaceChanged(gl: GL10?, width: Int, height: Int) {
        Log.d(TAG, "onSurfaceChanged: ${width}x${height}")
        
        screenWidth = width
        screenHeight = height
        
        GLES30.glViewport(0, 0, width, height)
        
        // 更新玻璃半径（基于屏幕尺寸）
        val minDimension = min(width, height)
        glassRadius = (minDimension * 0.15f) / minDimension // 归一化
        targetGlassRadius = glassRadius
        glassBlur = glassRadius * 0.5f
        
        GLUtils.checkGLError("onSurfaceChanged")
    }
    
    override fun onDrawFrame(gl: GL10?) {
        // 清除屏幕
        GLES30.glClear(GLES30.GL_COLOR_BUFFER_BIT)

        if (shaderProgram == 0) {
            return
        }

        // 如果没有背景纹理，不渲染任何内容
        if (backgroundTexture == 0) {
            return
        }
        
        // 使用着色器程序
        GLES30.glUseProgram(shaderProgram)

        // 计算动画参数
        val currentTime = System.currentTimeMillis()
        val elapsed = (currentTime - startTime) / 1000.0f

        // 动态变形参数
        val n1 = 0.005f
        val n2 = 1.5f
        val zx = sin(elapsed * n2) * n1 + 1.0f
        val zy = cos(elapsed * n2) * n1 + 1.0f

        // 平滑更新玻璃半径
        val dt = 0.016f // 假设60fps
        val num = max(0f, min(0.96f, 6.4f * dt))
        glassRadius += (targetGlassRadius - glassRadius) * num
        glassBlur = glassRadius * 0.5f

        // 设置uniform变量
        val glassCenterPixels = Pair(
            glassCenter.first * screenWidth,
            glassCenter.second * screenHeight
        )
        GLES30.glUniform2f(glassCenterLoc, glassCenterPixels.first, glassCenterPixels.second)

        val glassRadiusPixels = glassRadius * min(screenWidth, screenHeight)
        GLES30.glUniform1f(glassRadiusLoc, glassRadiusPixels)
        GLES30.glUniform1f(glassBlurLoc, glassBlur * min(screenWidth, screenHeight))

        // 玻璃颜色 (蓝色)
        GLES30.glUniform4f(glassColorLoc, 51f / 255f, 102f / 255f, 1.0f, 0.0f)

        // 光源位置 (左上角)
        GLES30.glUniform2f(lightPosLoc, screenWidth * 0.25f, screenHeight * 0.25f)

        GLES30.glUniform2f(contrlLoc, zx, zy)
        GLES30.glUniform2f(zoomLoc, 1.0f, 1.0f)
        GLES30.glUniform2f(resolutionLoc, screenWidth.toFloat(), screenHeight.toFloat())

        // 设置矩形玻璃尺寸 - 宽800，高400（像素）
        GLES30.glUniform2f(glassSizeLoc, 800.0f, 400.0f)

        // 设置圆角半径 - 30dp转换为像素（假设密度为3.0，即30dp = 90px）
        val cornerRadiusPx = 90.0f // 30dp * 3.0 density
        GLES30.glUniform1f(glassCornerRadiusLoc, cornerRadiusPx)
        
        // 绑定背景纹理
        GLES30.glActiveTexture(GLES30.GL_TEXTURE0)
        GLES30.glBindTexture(GLES30.GL_TEXTURE_2D, backgroundTexture)
        GLES30.glUniform1i(backgroundTextureLoc, 0)
        
        // 绘制全屏四边形
        GLES30.glBindVertexArray(vao)
        GLES30.glDrawElements(GLES30.GL_TRIANGLES, 6, GLES30.GL_UNSIGNED_INT, 0)
        GLES30.glBindVertexArray(0)
        
        GLUtils.checkGLError("onDrawFrame")
    }
    
    /**
     * 设置背景位图
     */
    fun setBackgroundBitmap(bitmap: Bitmap) {
        Log.d(TAG, "Setting background bitmap: ${bitmap.width}x${bitmap.height}")
        backgroundBitmap = bitmap

        // 立即创建纹理
        createBackgroundTexture()
    }
    
    /**
     * 创建背景纹理
     */
    private fun createBackgroundTexture() {
        backgroundBitmap?.let { bitmap ->
            // 删除旧纹理
            if (backgroundTexture != 0) {
                GLES30.glDeleteTextures(1, intArrayOf(backgroundTexture), 0)
            }

            backgroundTexture = GLUtils.createTexture(bitmap)
            Log.d(TAG, "Background texture created: $backgroundTexture")
        }
    }


    
    /**
     * 更新玻璃中心位置
     */
    fun updateGlassCenter(x: Float, y: Float) {
        if (screenWidth > 0 && screenHeight > 0) {
            glassCenter = Pair(
                x / screenWidth,
                y / screenHeight // 不翻转Y坐标，与着色器保持一致
            )
            Log.d(TAG, "Glass center updated: (${glassCenter.first}, ${glassCenter.second}) from touch ($x, $y)")
        }
    }
    
    /**
     * 更新玻璃半径
     */
    fun updateGlassRadius(scale: Float) {
        val minDimension = min(screenWidth, screenHeight)
        targetGlassRadius = (scale * minDimension * 0.15f) / minDimension
        targetGlassRadius = targetGlassRadius.coerceIn(0.05f, 0.4f)
    }
    
    /**
     * 清理资源
     */
    fun cleanup() {
        if (shaderProgram != 0) {
            GLES30.glDeleteProgram(shaderProgram)
            shaderProgram = 0
        }
        
        if (backgroundTexture != 0) {
            GLES30.glDeleteTextures(1, intArrayOf(backgroundTexture), 0)
            backgroundTexture = 0
        }
        
        if (vao != 0) {
            GLES30.glDeleteVertexArrays(1, intArrayOf(vao), 0)
            vao = 0
        }
        
        if (vbo != 0) {
            GLES30.glDeleteBuffers(1, intArrayOf(vbo), 0)
            vbo = 0
        }
        
        if (ebo != 0) {
            GLES30.glDeleteBuffers(1, intArrayOf(ebo), 0)
            ebo = 0
        }
    }
}
